import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/amplitude_service.dart';

void main() {
  group('AmplitudeService', () {
    late AmplitudeService amplitudeService;

    setUp(() {
      amplitudeService = AmplitudeService.instance;
    });

    test('should be a singleton', () {
      final instance1 = AmplitudeService.instance;
      final instance2 = AmplitudeService.instance;
      expect(instance1, same(instance2));
    });

    test('should not be initialized initially', () {
      expect(amplitudeService.isInitialized, false);
    });

    test('should have null currentUserId initially', () {
      expect(amplitudeService.currentUserId, null);
    });

    test('should handle trackEvent when not initialized', () async {
      // This should not throw an error
      await amplitudeService.trackEvent('test_event', {'key': 'value'});
      expect(amplitudeService.isInitialized, false);
    });

    test('should handle setUserId when not initialized', () async {
      // This should not throw an error
      await amplitudeService.setUserId('test_user_123');
      expect(amplitudeService.currentUserId, null);
    });

    test('should handle clearUserId when not initialized', () async {
      // This should not throw an error
      await amplitudeService.clearUserId();
      expect(amplitudeService.currentUserId, null);
    });

    test('should handle recordHttpRequest when not initialized', () async {
      // This should not throw an error
      await amplitudeService.recordHttpRequest(
        url: 'https://api.example.com/test',
        method: 'GET',
        statusCode: 200,
        startTime: DateTime.now().millisecondsSinceEpoch - 1000,
        endTime: DateTime.now().millisecondsSinceEpoch,
      );
      expect(amplitudeService.isInitialized, false);
    });

    test('should handle recordNetworkFailure when not initialized', () async {
      // This should not throw an error
      await amplitudeService.recordNetworkFailure(
        url: 'https://api.example.com/test',
        method: 'GET',
        startTime: DateTime.now().millisecondsSinceEpoch - 1000,
        endTime: DateTime.now().millisecondsSinceEpoch,
        errorMessage: 'Network error',
      );
      expect(amplitudeService.isInitialized, false);
    });

    test('should handle recordScreenView when not initialized', () async {
      // This should not throw an error
      await amplitudeService.recordScreenView('TestScreen', attributes: {
        'user_id': 'test_user',
        'tab': 'home',
      });
      expect(amplitudeService.isInitialized, false);
    });

    test('should handle recordError when not initialized', () async {
      // This should not throw an error
      await amplitudeService.recordError(
        Exception('Test error'),
        StackTrace.current,
        attributes: {'context': 'test'},
      );
      expect(amplitudeService.isInitialized, false);
    });

    test('should handle setUserProperties when not initialized', () async {
      // This should not throw an error
      await amplitudeService.setUserProperties({
        'plan': 'premium',
        'age': 25,
      });
      expect(amplitudeService.isInitialized, false);
    });

    test('should handle flush when not initialized', () async {
      // This should not throw an error
      await amplitudeService.flush();
      expect(amplitudeService.isInitialized, false);
    });
  });
}
