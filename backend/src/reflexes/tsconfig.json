{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "../", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "resolveJsonModule": true, "moduleResolution": "node", "baseUrl": ".", "paths": {"/opt/nodejs/*": ["../layers/utils-layer/nodejs/*"]}}, "include": ["*.ts", "utils/**/*.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}