{"name": "gameflex-kick-function", "version": "1.0.0", "description": "GameFlex Kick Integration Lambda Function", "main": "index.ts", "scripts": {"build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"node-fetch": "^2.6.7", "uuid": "^9.0.1"}, "devDependencies": {"@types/aws-lambda": "^8.10.0", "@types/node": "^20.0.0", "@types/uuid": "^9.0.0", "typescript": "^5.0.0"}, "keywords": ["aws", "lambda", "gameflex", "kick", "o<PERSON>h"], "author": "GameFlex Team", "license": "MIT"}