import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, PutCommand } from '@aws-sdk/lib-dynamodb';
import { SecretsManagerClient, GetSecretValueCommand } from '@aws-sdk/client-secrets-manager';
import { v4 as uuidv4 } from 'uuid';
import * as crypto from 'crypto';
const fetch = require('node-fetch');
import { logKickSignIn, logKickSignUp, logAccountLinkedKick, extractRequestMetadata } from '/opt/nodejs/user-history-service';
import { formatDisplayName } from '/opt/nodejs/display-name-config';
import { createResponse } from '/opt/nodejs/lambda-helpers';
import { UserAuthService } from '/opt/nodejs/user-auth-service';
import { AuthProvider } from '/opt/nodejs/user-auth';
import { generateAuthTokensWithContext } from '/opt/nodejs/jwt-auth-service';

// AWS Configuration
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

const dynamodbClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamodbClient);
const secretsManager = new SecretsManagerClient(awsConfig);

const USERS_TABLE = process.env.USERS_TABLE;
const USER_AUTHS_TABLE = process.env.USER_AUTHS_TABLE || process.env.USERAUTHS_TABLE_NAME;
const APP_CONFIG_SECRET_NAME = process.env.APP_CONFIG_SECRET_NAME;
const KICK_CONFIG_SECRET_NAME = process.env.KICK_CONFIG_SECRET_NAME;

// Initialize UserAuth service
const userAuthService = new UserAuthService(USER_AUTHS_TABLE);

// Kick API endpoints
const KICK_TOKEN_URL = 'https://id.kick.com/oauth/token';
const KICK_AUTH_URL = 'https://id.kick.com/oauth/authorize';
const KICK_USER_URL = 'https://api.kick.com/public/v1/users';

// Cache for Kick configuration
let kickConfig: { clientId: string; clientSecret: string } | null = null;

// PKCE helper functions
const generateCodeVerifier = (): string => {
    return crypto.randomBytes(32).toString('base64url');
};

const generateCodeChallenge = (codeVerifier: string): string => {
    return crypto.createHash('sha256').update(codeVerifier).digest('base64url');
};

// In-memory storage for PKCE state (in production, use Redis or DynamoDB)
const pkceStorage = new Map<string, { codeVerifier: string; state: string; timestamp: number }>();

// Clean up expired PKCE entries (older than 10 minutes)
const cleanupExpiredPKCE = () => {
    const now = Date.now();
    const tenMinutes = 10 * 60 * 1000;

    for (const [key, value] of pkceStorage.entries()) {
        if (now - value.timestamp > tenMinutes) {
            pkceStorage.delete(key);
        }
    }
};

interface KickTokenResponse {
    access_token: string;
    token_type: string;
    expires_in: number;
    refresh_token?: string;
    scope: string;
}

interface KickUser {
    user_id: number;
    name: string;
    email?: string;
    profile_picture?: string;
}

interface KickUserResponse {
    data: KickUser[];
    message: string;
}

// Helper function to get Kick configuration from Secrets Manager
const getKickConfig = async (): Promise<{ clientId: string; clientSecret: string } | null> => {
    if (kickConfig) {
        return kickConfig;
    }

    const secretName = KICK_CONFIG_SECRET_NAME || APP_CONFIG_SECRET_NAME;
    const useKickSecret = !!KICK_CONFIG_SECRET_NAME;

    if (!secretName) {
        console.error('No secret name configured for Kick');
        return null;
    }

    try {
        const command = new GetSecretValueCommand({
            SecretId: secretName,
        });
        const response = await secretsManager.send(command);

        if (!response.SecretString) {
            throw new Error('Secret value is empty');
        }

        const secrets = JSON.parse(response.SecretString);

        kickConfig = {
            clientId: secrets.clientId || secrets.kickClientId,
            clientSecret: secrets.clientSecret || secrets.kickClientSecret
        };

        if (!kickConfig.clientId || !kickConfig.clientSecret) {
            throw new Error(`Kick client ID or secret not found in ${useKickSecret ? 'Kick config' : 'app config'} secret`);
        }

        return kickConfig;
    } catch (error) {
        console.error('Error getting Kick configuration:', error);
        return null;
    }
};

// Helper function to exchange authorization code for access token
const exchangeCodeForToken = async (authCode: string, redirectUri: string, codeVerifier: string): Promise<KickTokenResponse> => {
    const config = await getKickConfig();
    if (!config) {
        throw new Error('Kick configuration not available');
    }

    const tokenParams = new URLSearchParams({
        client_id: config.clientId,
        client_secret: config.clientSecret,
        code: authCode,
        grant_type: 'authorization_code',
        redirect_uri: redirectUri,
        code_verifier: codeVerifier
    });

    const tokenResponse = await fetch(KICK_TOKEN_URL, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: tokenParams.toString()
    });

    if (!tokenResponse.ok) {
        const errorText = await tokenResponse.text();
        console.error('Kick token exchange failed:', errorText);
        throw new Error(`Failed to exchange code for token: ${tokenResponse.status} ${errorText}`);
    }

    return await tokenResponse.json();
};

// Helper function to get Kick user profile
const getKickUserProfile = async (accessToken: string): Promise<KickUser> => {
    const userResponse = await fetch(KICK_USER_URL, {
        headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Accept': 'application/json'
        }
    });

    if (!userResponse.ok) {
        const errorText = await userResponse.text();
        console.error('Kick user profile fetch failed:', errorText);
        throw new Error(`Failed to get user profile: ${userResponse.status} ${errorText}`);
    }

    const userData: KickUserResponse = await userResponse.json();

    if (!userData.data || userData.data.length === 0) {
        throw new Error('No user data returned from Kick API');
    }

    return userData.data[0]; // Return the first user from the array
};

// Helper function to check if Kick account is already linked
const checkExistingKickAuth = async (kickUserId: string) => {
    try {
        const existingAuth = await userAuthService.findUserAuthByProvider({
            provider: AuthProvider.KICK,
            providerUserId: kickUserId
        });
        if (existingAuth) {
            // Get the associated user
            const userCommand = new GetCommand({
                TableName: USERS_TABLE,
                Key: { id: existingAuth.userId }
            });
            const userResult = await dynamodb.send(userCommand);

            return {
                userAuth: existingAuth,
                user: userResult.Item
            };
        }
        return null;
    } catch (error) {
        console.error('Error checking existing Kick auth:', error);
        return null;
    }
};

// Helper function to generate random username
const generateRandomUsername = async (): Promise<string> => {
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    const username = `gamer_${randomSuffix}`;

    // Check if username is available
    try {
        const existingUser = await dynamodb.send(new GetCommand({
            TableName: USERS_TABLE,
            Key: { username: username }
        }));

        if (existingUser.Item) {
            // Username taken, try again
            return generateRandomUsername();
        }

        return username;
    } catch (error) {
        console.error('Error checking username availability:', error);
        return username; // Return anyway, let the creation handle conflicts
    }
};

// Start Kick OAuth flow
const startKickAuth = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const config = await getKickConfig();
        if (!config) {
            return createResponse(500, { error: 'Kick configuration not available' });
        }

        // Clean up expired PKCE entries
        cleanupExpiredPKCE();

        // Get userId from query parameters if linking account
        const userId = event.queryStringParameters?.userId;

        // Generate PKCE parameters
        const codeVerifier = generateCodeVerifier();
        const codeChallenge = generateCodeChallenge(codeVerifier);

        // Generate the Kick OAuth URL
        const redirectUri = `https://${event.headers.Host}/kick/callback`;
        const scopes = 'user:read';

        // Use userId as state if available (for linking), otherwise use 'signin' (for sign in)
        const state = userId || 'signin';

        // Generate a unique session ID for PKCE storage
        const sessionId = uuidv4();

        // Store PKCE parameters
        pkceStorage.set(sessionId, {
            codeVerifier,
            state,
            timestamp: Date.now()
        });

        const authUrl = `${KICK_AUTH_URL}?` + new URLSearchParams({
            client_id: config.clientId,
            response_type: 'code',
            redirect_uri: redirectUri,
            scope: scopes,
            code_challenge: codeChallenge,
            code_challenge_method: 'S256',
            state: sessionId // Use sessionId as state to retrieve PKCE parameters later
        }).toString();

        return createResponse(200, {
            authUrl,
            message: 'Redirect user to this URL to authenticate with Kick'
        });
    } catch (error) {
        console.error('Error starting Kick auth:', error);
        return createResponse(500, {
            error: 'Failed to start Kick authentication',
            details: (error as Error).message
        });
    }
};

// Handle Kick OAuth callback
const handleKickCallback = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        console.log('Kick callback received:', JSON.stringify(event.queryStringParameters, null, 2));

        const { code, state } = event.queryStringParameters || {};

        if (!code) {
            return createResponse(400, { error: 'Authorization code is required' });
        }

        if (!state) {
            return createResponse(400, { error: 'State parameter is required' });
        }

        // Return a simple success page that will trigger the frontend to complete the authentication
        const successHtml = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Kick Authentication</title>
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                    .success { color: #53FC18; }
                    .loading { color: #2196F3; }
                </style>
            </head>
            <body>
                <h1 class="success">✓ Kick Authentication Successful</h1>
                <p class="loading">Completing authentication...</p>
                <script>
                    // Post message to parent window (mobile app webview)
                    if (window.ReactNativeWebView) {
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                            type: 'kick_auth_success',
                            code: '${code}',
                            state: '${state}'
                        }));
                    }
                    // For web browsers, redirect to a custom URL scheme
                    else {
                        window.location.href = 'io.gameflex.oauth://kick/callback?code=${encodeURIComponent(code)}&state=${encodeURIComponent(state)}';
                    }
                </script>
            </body>
            </html>
        `;

        return {
            statusCode: 200,
            headers: {
                'Content-Type': 'text/html',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
                'Access-Control-Allow-Methods': 'GET,POST,OPTIONS'
            },
            body: successHtml
        };
    } catch (error) {
        console.error('Error handling Kick callback:', error);
        return createResponse(500, {
            error: 'Internal server error during Kick authentication',
            details: (error as Error).message
        });
    }
};

// Link Kick account to existing user
const linkKickAccount = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const body = JSON.parse(event.body || '{}');
        const { authCode, state } = body;

        if (!authCode) {
            return createResponse(400, {
                error: 'Authorization code is required'
            });
        }

        if (!state) {
            return createResponse(400, {
                error: 'State parameter is required for PKCE verification'
            });
        }

        // Retrieve PKCE parameters using the state (session ID)
        const pkceData = pkceStorage.get(state);
        if (!pkceData) {
            return createResponse(400, {
                error: 'Invalid or expired session'
            });
        }

        // Clean up the session
        pkceStorage.delete(state);

        // The state should contain the user ID for linking
        const userId = pkceData.state;
        if (userId === 'signin') {
            return createResponse(400, {
                error: 'Invalid state for account linking'
            });
        }

        // Exchange authorization code for access token
        const redirectUri = `https://${event.headers.Host}/kick/callback`;
        const tokenResponse = await exchangeCodeForToken(authCode, redirectUri, pkceData.codeVerifier);

        // Get user profile
        const kickUser = await getKickUserProfile(tokenResponse.access_token);

        // Check if Kick account is already linked to another user
        const existingAuth = await checkExistingKickAuth(kickUser.user_id.toString());
        if (existingAuth && existingAuth.user && existingAuth.user.id !== userId) {
            return createResponse(400, {
                error: 'This Kick account is already linked to another user'
            });
        }

        // Link the account
        await userAuthService.createUserAuth({
            userId: userId,
            provider: AuthProvider.KICK,
            providerUserId: kickUser.user_id.toString(),
            username: kickUser.name,
            accessToken: tokenResponse.access_token,
            refreshToken: tokenResponse.refresh_token,
            tokenExpiresAt: new Date(Date.now() + tokenResponse.expires_in * 1000).toISOString(),
            providerData: {
                email: kickUser.email,
                profilePicture: kickUser.profile_picture
            }
        });

        // Log the account linking
        const requestMetadata = extractRequestMetadata(event);
        await logAccountLinkedKick(userId, kickUser.user_id.toString(), kickUser.name, requestMetadata.ipAddress, requestMetadata.userAgent);

        return createResponse(200, {
            message: 'Kick account linked successfully',
            kickUsername: kickUser.name,
            kickDisplayName: kickUser.name
        });

    } catch (error) {
        console.error('Error linking Kick account:', error);
        return createResponse(500, {
            error: 'Failed to link Kick account',
            details: (error as Error).message
        });
    }
};

// Sign in with Kick
const kickSignIn = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const body = JSON.parse(event.body || '{}');
        const { authCode } = body;

        if (!authCode) {
            return createResponse(400, {
                error: 'Authorization code is required'
            });
        }

        // For Kick, we need to get the state parameter as well to retrieve PKCE data
        // The mobile app should send both code and state from the callback URL
        const { state } = JSON.parse(event.body || '{}');

        if (!state) {
            return createResponse(400, {
                error: 'State parameter is required for PKCE verification'
            });
        }

        // Retrieve PKCE parameters using the state (session ID)
        const pkceData = pkceStorage.get(state);
        if (!pkceData) {
            return createResponse(400, {
                error: 'Invalid or expired session'
            });
        }

        // Clean up the session
        pkceStorage.delete(state);

        // Exchange authorization code for access token
        const redirectUri = `https://${event.headers.Host}/kick/callback`;
        const tokenResponse = await exchangeCodeForToken(authCode, redirectUri, pkceData.codeVerifier);

        // Get user profile
        const kickUser = await getKickUserProfile(tokenResponse.access_token);

        // Check if user exists
        const existingAuth = await checkExistingKickAuth(kickUser.user_id.toString());

        if (existingAuth) {
            // User exists, sign them in
            const user = existingAuth.user;

            if (!user) {
                return createResponse(500, {
                    error: 'User data not found for existing auth'
                });
            }

            // Update the auth record with new tokens
            await userAuthService.updateUserAuth({
                id: existingAuth.userAuth.id,
                accessToken: tokenResponse.access_token,
                refreshToken: tokenResponse.refresh_token,
                tokenExpiresAt: new Date(Date.now() + tokenResponse.expires_in * 1000).toISOString(),
                providerData: {
                    email: kickUser.email,
                    profilePicture: kickUser.profile_picture
                }
            });

            // Generate auth tokens
            const requestMetadata = extractRequestMetadata(event);
            const authTokens = await generateAuthTokensWithContext(user.id);

            // Log the sign-in
            await logKickSignIn(user.id, kickUser.user_id.toString(), kickUser.name, requestMetadata.ipAddress, requestMetadata.userAgent);

            return createResponse(200, {
                message: 'Kick sign in successful',
                user: {
                    id: user.id,
                    email: user.email || '',
                    username: user.username,
                    displayName: user.displayName,
                    displayNameProvider: user.displayNameProvider,
                    createdAt: user.createdAt,
                    updatedAt: user.updatedAt
                },
                tokens: {
                    accessToken: authTokens.accessToken,
                    refreshToken: authTokens.refreshToken,
                    idToken: authTokens.idToken
                },
                kickProfile: {
                    username: kickUser.name,
                    profilePicture: kickUser.profile_picture
                }
            });
        } else {
            // New user, create account
            const newUserId = uuidv4();
            const username = await generateRandomUsername();

            // Create user record in DynamoDB
            const userRecord = {
                id: newUserId,
                username: username,
                email: kickUser.email || null,
                displayName: formatDisplayName(kickUser.name, 'kick'), // Store formatted displayName like "[kick]Username123"
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            await dynamodb.send(new PutCommand({
                TableName: USERS_TABLE,
                Item: userRecord
            }));

            // Create Kick authentication record
            await userAuthService.createUserAuth({
                userId: newUserId,
                provider: AuthProvider.KICK,
                providerUserId: kickUser.user_id.toString(),
                username: kickUser.name,
                accessToken: tokenResponse.access_token,
                refreshToken: tokenResponse.refresh_token,
                tokenExpiresAt: new Date(Date.now() + tokenResponse.expires_in * 1000).toISOString(),
                providerData: {
                    email: kickUser.email,
                    profilePicture: kickUser.profile_picture
                },
                isPrimary: true,
                isVerified: true
            });

            // Generate authentication tokens for new user
            const authTokens = await generateAuthTokensWithContext(newUserId);

            // Log Kick sign up
            const requestMetadata = extractRequestMetadata(event);
            await logKickSignUp(newUserId, kickUser.user_id.toString(), kickUser.name, requestMetadata.ipAddress, requestMetadata.userAgent);

            return createResponse(201, {
                message: 'Kick account created and signed in successfully',
                user: {
                    id: newUserId,
                    email: kickUser.email || '',
                    username: username,
                    displayName: userRecord.displayName,
                    displayNameProvider: 'kick',
                    createdAt: userRecord.createdAt,
                    updatedAt: userRecord.updatedAt
                },
                tokens: {
                    accessToken: authTokens.accessToken,
                    refreshToken: authTokens.refreshToken,
                    idToken: authTokens.idToken
                },
                kickProfile: {
                    username: kickUser.name,
                    profilePicture: kickUser.profile_picture
                }
            });
        }

    } catch (error) {
        console.error('KickSignIn error:', error);
        return createResponse(500, {
            error: 'Failed to sign in with Kick',
            details: (error as Error).message
        });
    }
};

// Main Lambda handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('Kick Auth Event:', JSON.stringify(event, null, 2));

    const path = event.path;
    const httpMethod = event.httpMethod;

    try {
        // Route to appropriate handler based on path and method
        if (path === '/kick/auth' && httpMethod === 'GET') {
            return await startKickAuth(event);
        } else if (path === '/kick/callback' && httpMethod === 'GET') {
            return await handleKickCallback(event);
        } else if (path === '/kick/link' && httpMethod === 'POST') {
            return await linkKickAccount(event);
        } else if (path === '/kick/signin' && httpMethod === 'POST') {
            return await kickSignIn(event);
        } else {
            return createResponse(404, { error: 'Endpoint not found' });
        }
    } catch (error) {
        console.error('Kick Auth Handler Error:', error);
        return createResponse(500, {
            error: 'Internal server error',
            details: (error as Error).message
        });
    }
};
