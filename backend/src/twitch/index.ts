import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, PutCommand } from '@aws-sdk/lib-dynamodb';
import { SecretsManagerClient, GetSecretValueCommand } from '@aws-sdk/client-secrets-manager';
import { v4 as uuidv4 } from 'uuid';
const fetch = require('node-fetch');
import { logTwitchSignIn, logTwitchSignUp, logAccountLinkedTwitch, extractRequestMetadata } from '/opt/nodejs/user-history-service';
import { formatDisplayName } from '/opt/nodejs/display-name-config';
import { createResponse } from '/opt/nodejs/lambda-helpers';
import { UserAuthService } from '/opt/nodejs/user-auth-service';
import { AuthProvider } from '/opt/nodejs/user-auth';
import { generateAuthTokensWithContext } from '/opt/nodejs/jwt-auth-service';

// AWS Configuration
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

const dynamodbClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamodbClient);
const secretsManager = new SecretsManagerClient(awsConfig);

const USERS_TABLE = process.env.USERS_TABLE;
const USER_AUTHS_TABLE = process.env.USER_AUTHS_TABLE || process.env.USERAUTHS_TABLE_NAME;
const APP_CONFIG_SECRET_NAME = process.env.APP_CONFIG_SECRET_NAME;
const TWITCH_CONFIG_SECRET_NAME = process.env.TWITCH_CONFIG_SECRET_NAME;

// Initialize UserAuth service
const userAuthService = new UserAuthService(USER_AUTHS_TABLE);

// Twitch API endpoints
const TWITCH_TOKEN_URL = 'https://id.twitch.tv/oauth2/token';
const TWITCH_AUTH_URL = 'https://id.twitch.tv/oauth2/authorize';
const TWITCH_USER_URL = 'https://api.twitch.tv/helix/users';

let twitchConfig: {
    clientId: string;
    clientSecret: string;
} | null = null;

// TypeScript interfaces
interface TwitchLinkRequest {
    authCode: string; // OAuth authorization code for linking
}

interface TwitchSignInRequest {
    authCode: string; // OAuth authorization code for sign in
}

interface UserRecord {
    id: string;
    email?: string; // Optional - stored in UserAuths table for third-party auth
    username?: string;
    firstName?: string;
    lastName?: string;
    displayName?: string; // Always includes provider prefix like "[twitch]Username123"
    createdAt: string;
    updatedAt: string;
}

interface TwitchTokenResponse {
    access_token: string;
    refresh_token: string;
    expires_in: number;
    scope: string[];
    token_type: string;
}

interface TwitchUser {
    id: string;
    login: string;
    display_name: string;
    type: string;
    broadcaster_type: string;
    description: string;
    profile_image_url: string;
    offline_image_url: string;
    view_count: number;
    email?: string;
    created_at: string;
}

interface TwitchUserResponse {
    data: TwitchUser[];
}

// Helper function to get Twitch configuration from Secrets Manager
const getTwitchConfig = async (): Promise<{ clientId: string; clientSecret: string } | null> => {
    if (twitchConfig) {
        return twitchConfig;
    }

    const secretName = TWITCH_CONFIG_SECRET_NAME || APP_CONFIG_SECRET_NAME;
    const useTwitchSecret = !!TWITCH_CONFIG_SECRET_NAME;

    if (!secretName) {
        console.error('No secret name configured for Twitch');
        return null;
    }

    try {
        const command = new GetSecretValueCommand({
            SecretId: secretName,
        });
        const response = await secretsManager.send(command);

        if (!response.SecretString) {
            throw new Error('Secret value is empty');
        }

        const secrets = JSON.parse(response.SecretString);

        twitchConfig = {
            clientId: secrets.clientId || secrets.twitchClientId,
            clientSecret: secrets.clientSecret || secrets.twitchClientSecret
        };

        if (!twitchConfig.clientId || !twitchConfig.clientSecret) {
            throw new Error(`Twitch client ID or secret not found in ${useTwitchSecret ? 'Twitch config' : 'app config'} secret`);
        }

        return twitchConfig;
    } catch (error) {
        console.error('Error getting Twitch config:', error);
        throw new Error(`Failed to retrieve Twitch configuration from ${secretName}: ${(error as Error).message}`);
    }
};

// Helper function to exchange authorization code for access token
const exchangeCodeForToken = async (authCode: string, redirectUri: string): Promise<TwitchTokenResponse> => {
    const config = await getTwitchConfig();
    if (!config) {
        throw new Error('Twitch configuration not available');
    }

    const tokenParams = new URLSearchParams({
        client_id: config.clientId,
        client_secret: config.clientSecret,
        code: authCode,
        grant_type: 'authorization_code',
        redirect_uri: redirectUri
    });

    const tokenResponse = await fetch(TWITCH_TOKEN_URL, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: tokenParams.toString()
    });

    if (!tokenResponse.ok) {
        const errorText = await tokenResponse.text();
        throw new Error(`Token exchange failed: ${tokenResponse.status} - ${errorText}`);
    }

    return await tokenResponse.json();
};

// Helper function to get Twitch user profile
const getTwitchUserProfile = async (accessToken: string): Promise<TwitchUser> => {
    const config = await getTwitchConfig();
    if (!config) {
        throw new Error('Twitch configuration not available');
    }

    const userResponse = await fetch(TWITCH_USER_URL, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Client-Id': config.clientId
        }
    });

    if (!userResponse.ok) {
        const errorText = await userResponse.text();
        throw new Error(`Failed to get Twitch user profile: ${userResponse.status} - ${errorText}`);
    }

    const userData: TwitchUserResponse = await userResponse.json();

    if (!userData.data || userData.data.length === 0) {
        throw new Error('No user data returned from Twitch API');
    }

    return userData.data[0];
};

// Helper function to check existing Twitch authentication (active only)
const checkExistingTwitchAuth = async (twitchUserId: string): Promise<any | null> => {
    try {
        const userAuth = await userAuthService.findUserAuthByProvider({
            provider: AuthProvider.TWITCH,
            providerUserId: twitchUserId
        });

        if (userAuth) {
            // Get the user record
            const getUserCommand = new GetCommand({
                TableName: USERS_TABLE,
                Key: { id: userAuth.userId }
            });
            const userResult = await dynamodb.send(getUserCommand);

            if (userResult.Item) {
                return {
                    userAuth,
                    user: userResult.Item as UserRecord
                };
            }
        }

        return null;
    } catch (error) {
        console.error('Error checking existing Twitch authentication:', error);
        return null;
    }
};



// Main Lambda handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('Twitch Auth Event:', JSON.stringify(event, null, 2));

    const path = event.path;
    const httpMethod = event.httpMethod;

    try {
        // Route to appropriate handler based on path and method
        if (path === '/twitch/auth' && httpMethod === 'GET') {
            return await startTwitchAuth(event);
        } else if (path === '/twitch/callback' && httpMethod === 'GET') {
            return await handleTwitchCallback(event);
        } else if (path === '/twitch/link' && httpMethod === 'POST') {
            return await linkTwitchAccount(event);
        } else if (path === '/twitch/signin' && httpMethod === 'POST') {
            return await twitchSignIn(event);
        } else {
            return createResponse(404, { error: 'Endpoint not found' });
        }
    } catch (error) {
        console.error('Twitch Auth Handler Error:', error);
        return createResponse(500, {
            error: 'Internal server error',
            details: (error as Error).message
        });
    }
};

// Start Twitch OAuth flow
const startTwitchAuth = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const config = await getTwitchConfig();
        if (!config) {
            return createResponse(500, { error: 'Twitch configuration not available' });
        }

        // Get userId from query parameters if linking account
        const userId = event.queryStringParameters?.userId;

        // Generate the Twitch OAuth URL
        const redirectUri = `https://${event.headers.Host}/twitch/callback`;
        const scopes = 'user:read:email';

        // Use userId as state if available (for linking), otherwise use 'signin' (for sign in)
        const state = userId || 'signin';

        const authUrl = `${TWITCH_AUTH_URL}?` + new URLSearchParams({
            client_id: config.clientId,
            response_type: 'code',
            redirect_uri: redirectUri,
            scope: scopes,
            state: state
        }).toString();

        return createResponse(200, {
            authUrl,
            message: 'Redirect user to this URL to authenticate with Twitch'
        });
    } catch (error) {
        console.error('StartTwitchAuth error:', error);
        return createResponse(500, {
            error: 'Failed to start Twitch authentication',
            details: (error as Error).message
        });
    }
};

// Handle Twitch OAuth callback
const handleTwitchCallback = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        console.log('Twitch callback received:', JSON.stringify(event.queryStringParameters, null, 2));

        const { code, state } = event.queryStringParameters || {};

        if (!code) {
            return createResponse(400, { error: 'Authorization code is required' });
        }

        if (!state) {
            return createResponse(400, { error: 'State parameter is required' });
        }

        // Return a simple success page that will trigger the frontend to complete the authentication
        const successHtml = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Twitch Authentication</title>
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                    .success { color: #9146FF; }
                    .loading { color: #2196F3; }
                </style>
            </head>
            <body>
                <h1 class="success">✓ Twitch Authentication Successful</h1>
                <p class="loading">Completing authentication...</p>
                <script>
                    // Post message to parent window (mobile app webview)
                    if (window.ReactNativeWebView) {
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                            type: 'twitch_auth_success',
                            code: '${code}',
                            state: '${state}'
                        }));
                    }
                    // For web browsers, redirect to a custom URL scheme
                    else {
                        window.location.href = 'io.gameflex.oauth://twitch/callback?code=${encodeURIComponent(code)}&state=${encodeURIComponent(state)}';
                    }
                </script>
            </body>
            </html>
        `;

        return {
            statusCode: 200,
            headers: {
                'Content-Type': 'text/html',
                'Access-Control-Allow-Origin': '*'
            },
            body: successHtml
        };

    } catch (error) {
        console.error('HandleTwitchCallback error:', error);
        return createResponse(500, {
            error: 'Failed to handle Twitch callback',
            details: (error as Error).message
        });
    }
};

// Link Twitch account to existing user
const linkTwitchAccount = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const request: TwitchLinkRequest = JSON.parse(event.body);
        const { authCode } = request;

        if (!authCode) {
            return createResponse(400, { error: 'Authorization code is required' });
        }

        // Get user ID from the authorizer context
        const userId = event.requestContext.authorizer?.userId;
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Exchange authorization code for access token
        const redirectUri = `https://${event.headers.Host}/twitch/callback`;
        const tokenData = await exchangeCodeForToken(authCode, redirectUri);

        // Get Twitch user profile
        const twitchProfile = await getTwitchUserProfile(tokenData.access_token);
        const twitchUserId = twitchProfile.id;
        const twitchUsername = twitchProfile.login;

        // Check if this Twitch account is already linked to another user
        const existingTwitchAuth = await checkExistingTwitchAuth(twitchUserId);
        if (existingTwitchAuth) {
            return createResponse(409, {
                error: 'This Twitch account is already linked to another user',
                details: 'Please unlink it from the other account first'
            });
        }

        // Get the current user
        const getUserCommand = new GetCommand({
            TableName: USERS_TABLE,
            Key: { id: userId }
        });
        const userResult = await dynamodb.send(getUserCommand);

        if (!userResult.Item) {
            return createResponse(404, { error: 'User not found' });
        }

        const user = userResult.Item as UserRecord;

        // Create Twitch authentication record
        await userAuthService.createUserAuth({
            userId,
            provider: AuthProvider.TWITCH,
            providerUserId: twitchUserId,
            username: twitchUsername, // Store Twitch username
            email: twitchProfile.email, // Store email if provided
            accessToken: tokenData.access_token,
            refreshToken: tokenData.refresh_token,
            tokenExpiresAt: new Date(Date.now() + tokenData.expires_in * 1000).toISOString(),
            providerData: {
                displayName: twitchProfile.display_name,
                profileImageUrl: twitchProfile.profile_image_url,
                broadcasterType: twitchProfile.broadcaster_type,
                description: twitchProfile.description,
                viewCount: twitchProfile.view_count,
                createdAt: twitchProfile.created_at
            },
            isPrimary: false, // Twitch is typically not primary auth
            isVerified: true
        });

        // Log Twitch account linking
        try {
            const { ipAddress, userAgent } = extractRequestMetadata(event);
            await logAccountLinkedTwitch(userId, twitchUserId, twitchUsername, ipAddress, userAgent);
        } catch (historyError) {
            console.error('Failed to log Twitch account linking:', historyError);
            // Don't fail the linking if history logging fails
        }

        return createResponse(200, {
            message: 'Twitch account linked successfully',
            twitchUsername: twitchUsername,
            twitchDisplayName: twitchProfile.display_name
        });

    } catch (error) {
        console.error('LinkTwitchAccount error:', error);
        return createResponse(500, {
            error: 'Failed to link Twitch account',
            details: (error as Error).message
        });
    }
};

// Twitch Sign In (create account or sign in)
const twitchSignIn = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const request: TwitchSignInRequest = JSON.parse(event.body);
        const { authCode } = request;

        if (!authCode) {
            return createResponse(400, { error: 'Authorization code is required' });
        }

        // Exchange authorization code for access token
        const redirectUri = `https://${event.headers.Host}/twitch/callback`;
        const tokenData = await exchangeCodeForToken(authCode, redirectUri);

        // Get Twitch user profile
        const twitchProfile = await getTwitchUserProfile(tokenData.access_token);
        const twitchUserId = twitchProfile.id;
        const twitchUsername = twitchProfile.login;

        // Check if this Twitch account is already linked
        const existingTwitchAuth = await checkExistingTwitchAuth(twitchUserId);

        if (existingTwitchAuth) {
            // User exists, sign them in
            const user = existingTwitchAuth.user;

            // Update Twitch authentication tokens
            await userAuthService.updateUserAuth({
                id: existingTwitchAuth.userAuth.id,
                username: twitchUsername, // Update username in case it changed
                accessToken: tokenData.access_token,
                refreshToken: tokenData.refresh_token,
                tokenExpiresAt: new Date(Date.now() + tokenData.expires_in * 1000).toISOString(),
                providerData: {
                    ...existingTwitchAuth.userAuth.providerData,
                    displayName: twitchProfile.display_name,
                    profileImageUrl: twitchProfile.profile_image_url,
                    broadcasterType: twitchProfile.broadcaster_type,
                    description: twitchProfile.description,
                    viewCount: twitchProfile.view_count
                },
                lastUsedAt: new Date().toISOString()
            });

            // Generate authentication tokens for third-party user
            const authTokens = await generateAuthTokensWithContext(user.id);

            // Log Twitch sign in
            try {
                const { ipAddress, userAgent } = extractRequestMetadata(event);
                await logTwitchSignIn(user.id, twitchUserId, twitchUsername, ipAddress, userAgent);
            } catch (historyError) {
                console.error('Failed to log Twitch sign in:', historyError);
                // Don't fail the sign in if history logging fails
            }

            return createResponse(200, {
                message: 'Twitch sign in successful',
                user: {
                    id: user.id,
                    email: user.email || '',
                    username: user.username,
                    displayName: user.displayName,
                    displayNameProvider: user.displayNameProvider,
                    createdAt: user.createdAt,
                    updatedAt: user.updatedAt
                },
                tokens: {
                    accessToken: authTokens.accessToken,
                    refreshToken: authTokens.refreshToken,
                    idToken: authTokens.idToken
                },
                twitchProfile: {
                    username: twitchUsername,
                    displayName: twitchProfile.display_name
                }
            });
        } else {
            // New user, create account (no Cognito needed for third-party auth)
            const newUserId = uuidv4();

            // Create user record in DynamoDB
            const userRecord: UserRecord = {
                id: newUserId,
                email: twitchProfile.email, // Store email if provided by Twitch
                displayName: formatDisplayName(twitchProfile.display_name, 'twitch'), // Format as "[twitch]Username123"
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
                // No cognitoUserId for third-party auth users
            };

            const putCommand = new PutCommand({
                TableName: USERS_TABLE,
                Item: userRecord
            });
            await dynamodb.send(putCommand);

            // Create Twitch authentication record
            await userAuthService.createUserAuth({
                userId: newUserId,
                provider: AuthProvider.TWITCH,
                providerUserId: twitchUserId,
                username: twitchUsername, // Store Twitch username
                email: twitchProfile.email, // Store email if provided
                accessToken: tokenData.access_token,
                refreshToken: tokenData.refresh_token,
                tokenExpiresAt: new Date(Date.now() + tokenData.expires_in * 1000).toISOString(),
                providerData: {
                    displayName: twitchProfile.display_name,
                    profileImageUrl: twitchProfile.profile_image_url,
                    broadcasterType: twitchProfile.broadcaster_type,
                    description: twitchProfile.description,
                    viewCount: twitchProfile.view_count,
                    createdAt: twitchProfile.created_at
                },
                isPrimary: true, // Twitch is primary for this user since it's their only auth
                isVerified: true
            });

            // Generate authentication tokens for new third-party user
            const authTokens = await generateAuthTokensWithContext(newUserId);

            // Log Twitch sign up
            try {
                const { ipAddress, userAgent } = extractRequestMetadata(event);
                await logTwitchSignUp(newUserId, twitchUserId, twitchUsername, ipAddress, userAgent);
            } catch (historyError) {
                console.error('Failed to log Twitch sign up:', historyError);
                // Don't fail the sign up if history logging fails
            }

            return createResponse(201, {
                message: 'Twitch account created and signed in successfully',
                user: {
                    id: newUserId,
                    email: twitchProfile.email || '',
                    username: null, // Will be set during username selection
                    displayName: formatDisplayName(twitchProfile.display_name, 'twitch'), // Return formatted displayName like "[twitch]Username123"
                    createdAt: userRecord.createdAt,
                    updatedAt: userRecord.updatedAt
                },
                tokens: {
                    accessToken: authTokens.accessToken,
                    refreshToken: authTokens.refreshToken,
                    idToken: authTokens.idToken
                },
                twitchProfile: {
                    username: twitchUsername,
                    displayName: twitchProfile.display_name
                }
            });
        }

    } catch (error) {
        console.error('TwitchSignIn error:', error);
        return createResponse(500, {
            error: 'Failed to sign in with Twitch',
            details: (error as Error).message
        });
    }
};
