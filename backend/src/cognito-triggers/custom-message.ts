import { CustomMessageTriggerEvent, CustomMessageTriggerHandler } from 'aws-lambda';
import * as fs from 'fs';
import * as path from 'path';

// Read email templates at module level for better performance
let verificationEmailHtml: string;
let verificationEmailText: string;
let passwordResetEmailHtml: string;
let passwordResetEmailText: string;
let mfaAuthenticationEmailHtml: string;
let mfaAuthenticationEmailText: string;

// Load verification email templates
try {
    // Templates are bundled with the Lambda function during deployment
    verificationEmailHtml = fs.readFileSync(
        path.join(__dirname, 'email-templates/verification-email.html'),
        'utf8'
    );
    console.log('Loaded verification HTML template');
} catch (error) {
    console.warn('Verification HTML template not found, using default template');
    verificationEmailHtml = `
        <!DOCTYPE html>
        <html>
        <head><title>Verify Your GameFlex Account</title></head>
        <body style="font-family: Arial, sans-serif; background-color: #f8f9fa; padding: 20px;">
            <div style="max-width: 600px; margin: 0 auto; background-color: white; padding: 40px; border-radius: 8px;">
                <h1 style="color: #6366f1; text-align: center;">🎮 GameFlex</h1>
                <h2>Welcome to GameFlex!</h2>
                <p>Your verification code is:</p>
                <div style="text-align: center; font-size: 32px; font-weight: bold; color: #6366f1; letter-spacing: 4px; margin: 20px 0;">{####}</div>
                <p>Enter this code in the GameFlex app to verify your account.</p>
                <p>Happy gaming!<br>The GameFlex Team</p>
            </div>
        </body>
        </html>
    `;
}

try {
    verificationEmailText = fs.readFileSync(
        path.join(__dirname, 'email-templates/verification-email.txt'),
        'utf8'
    );
    console.log('Loaded verification text template');
} catch (error) {
    console.warn('Verification text template not found, using default template');
    verificationEmailText = `
GameFlex - Verify Your Account

Welcome to GameFlex! We're excited to have you join our gaming community.

To complete your account setup, please use the verification code below:

Verification Code: {####}

This code will expire in 24 hours for security reasons.

If you didn't create a GameFlex account, you can safely ignore this email.

Happy gaming!
The GameFlex Team
    `;
}

// Load password reset email templates
try {
    passwordResetEmailHtml = fs.readFileSync(
        path.join(__dirname, 'email-templates/password-reset-email.html'),
        'utf8'
    );
    console.log('Loaded password reset HTML template');
} catch (error) {
    console.warn('Password reset HTML template not found, using default template');
    passwordResetEmailHtml = `
        <!DOCTYPE html>
        <html>
        <head><title>Reset Your GameFlex Password</title></head>
        <body style="font-family: Arial, sans-serif; background-color: #f8f9fa; padding: 20px;">
            <div style="max-width: 600px; margin: 0 auto; background-color: white; padding: 40px; border-radius: 8px;">
                <h1 style="color: #6366f1; text-align: center;">🎮 GameFlex</h1>
                <h2>Reset Your GameFlex Password</h2>
                <p>Your password reset code is:</p>
                <div style="text-align: center; font-size: 32px; font-weight: bold; color: #6366f1; letter-spacing: 4px; margin: 20px 0;">{####}</div>
                <p>Enter this code in the GameFlex app to reset your password.</p>
                <p>If you didn't request this, please ignore this email.</p>
                <p>The GameFlex Team</p>
            </div>
        </body>
        </html>
    `;
}

try {
    passwordResetEmailText = fs.readFileSync(
        path.join(__dirname, 'email-templates/password-reset-email.txt'),
        'utf8'
    );
    console.log('Loaded password reset text template');
} catch (error) {
    console.warn('Password reset text template not found, using default template');
    passwordResetEmailText = `
GameFlex - Reset Your Password

You requested to reset your GameFlex password.

Your password reset code is: {####}

This code will expire in 1 hour for security reasons.

If you didn't request this password reset, you can safely ignore this email.

The GameFlex Team
    `;
}

// Load MFA authentication email templates
try {
    mfaAuthenticationEmailHtml = fs.readFileSync(
        path.join(__dirname, 'email-templates/mfa-authentication-email.html'),
        'utf8'
    );
    console.log('Loaded MFA authentication HTML template');
} catch (error) {
    console.warn('MFA authentication HTML template not found, using default template');
    mfaAuthenticationEmailHtml = `
        <!DOCTYPE html>
        <html>
        <head><title>GameFlex Authentication Code</title></head>
        <body style="font-family: Arial, sans-serif; background-color: #f8f9fa; padding: 20px;">
            <div style="max-width: 600px; margin: 0 auto; background-color: white; padding: 40px; border-radius: 8px;">
                <h1 style="color: #6366f1; text-align: center;">🎮 GameFlex</h1>
                <h2>Authentication Required</h2>
                <p>Your authentication code is:</p>
                <div style="text-align: center; font-size: 32px; font-weight: bold; color: #6366f1; letter-spacing: 4px; margin: 20px 0;">{####}</div>
                <p>Enter this code to complete your sign-in.</p>
                <p>The GameFlex Team</p>
            </div>
        </body>
        </html>
    `;
}

try {
    mfaAuthenticationEmailText = fs.readFileSync(
        path.join(__dirname, 'email-templates/mfa-authentication-email.txt'),
        'utf8'
    );
    console.log('Loaded MFA authentication text template');
} catch (error) {
    console.warn('MFA authentication text template not found, using default template');
    mfaAuthenticationEmailText = `
GameFlex - Authentication Code

Your GameFlex authentication code is: {####}

Enter this code to complete your sign-in.

This code will expire in 5 minutes for security reasons.

The GameFlex Team
    `;
}

export const handler: CustomMessageTriggerHandler = async (event: CustomMessageTriggerEvent) => {
    console.log('Custom message trigger event:', JSON.stringify(event, null, 2));

    try {
        const { triggerSource, request, response } = event;
        const { codeParameter } = request;

        switch (triggerSource) {
            case 'CustomMessage_SignUp':
            case 'CustomMessage_ResendCode':
                // Email verification
                response.emailSubject = 'Verify your GameFlex account';
                response.emailMessage = verificationEmailHtml.replace(/\{####\}/g, codeParameter || '');
                // Also set SMS message if needed
                response.smsMessage = `GameFlex verification code: ${codeParameter}`;
                break;

            case 'CustomMessage_ForgotPassword':
                // Password reset
                response.emailSubject = 'Reset your GameFlex password';
                response.emailMessage = passwordResetEmailHtml.replace(/\{####\}/g, codeParameter || '');
                response.smsMessage = `GameFlex password reset code: ${codeParameter}`;
                break;

            case 'CustomMessage_AdminCreateUser':
                // Admin created user (invitation)
                response.emailSubject = 'Welcome to GameFlex - Verify your account';
                response.emailMessage = verificationEmailHtml.replace(/\{####\}/g, codeParameter || '');
                response.smsMessage = `GameFlex verification code: ${codeParameter}`;
                break;

            case 'CustomMessage_Authentication':
                // MFA authentication
                response.emailSubject = 'GameFlex authentication code';
                response.emailMessage = mfaAuthenticationEmailHtml.replace(/\{####\}/g, codeParameter || '');
                response.smsMessage = `GameFlex authentication code: ${codeParameter}`;
                break;

            default:
                console.log(`Unhandled trigger source: ${triggerSource}`);
                // Use default Cognito message
                break;
        }

        console.log('Custom message response:', {
            triggerSource,
            emailSubject: response.emailSubject,
            emailMessageLength: response.emailMessage?.length || 0,
            hasSmsMessage: !!response.smsMessage
        });

        return event;
    } catch (error) {
        console.error('Error in custom message trigger:', error);
        // Return the event unchanged to use default Cognito messages
        return event;
    }
};
