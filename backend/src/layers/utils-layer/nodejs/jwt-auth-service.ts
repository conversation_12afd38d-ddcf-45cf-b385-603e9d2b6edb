import jwt from 'jsonwebtoken';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { getSecret } from '@aws-lambda-powertools/parameters/secrets';

// Configure AWS SDK v3 clients
const dynamodbClient = new DynamoDBClient({
    region: process.env.AWS_REGION || 'us-west-2'
});
const dynamodb = DynamoDBDocumentClient.from(dynamodbClient);

// Environment variables
const JWT_SECRET_NAME = process.env.JWT_SECRET_NAME;
const USERS_TABLE = process.env.USERS_TABLE;
const USER_AUTHS_TABLE = process.env.USER_AUTHS_TABLE;

// Helper function to get JWT secret from AWS Secrets Manager with intelligent caching
const getJWTSecret = async (): Promise<string> => {
    if (!JWT_SECRET_NAME) {
        throw new Error('JWT_SECRET_NAME environment variable not set');
    }

    try {
        // AWS Lambda Powertools provides intelligent caching that survives across invocations
        // Cache TTL is 5 minutes by default, and it handles cache invalidation automatically
        const secretString = await getSecret(JWT_SECRET_NAME, {
            maxAge: 300, // Cache for 5 minutes
            transform: 'json' // Automatically parse JSON
        });

        if (!secretString || typeof secretString !== 'object') {
            throw new Error('JWT secret not found or invalid format');
        }

        const secretData = secretString as { secret_key?: string };
        const jwtSecret = secretData.secret_key;

        if (!jwtSecret) {
            throw new Error('JWT secret_key not found in secret');
        }

        return jwtSecret;
    } catch (error) {
        console.error('Failed to get JWT secret:', error);
        throw new Error('Failed to retrieve JWT secret');
    }
};

// TypeScript interfaces
export interface JWTPayload {
    sub: string; // User ID
    iat: number; // Issued at
    exp: number; // Expires at
    token_use: 'access' | 'refresh' | 'id';
    email?: string;
    username?: string;
    firstName?: string;
    lastName?: string;
    displayName?: string;
    authProviders?: string[]; // List of auth providers (email, xbox, twitch, apple)
    linkedUsernames?: string[]; // List of usernames from linked accounts
}

export interface AuthTokens {
    accessToken: string;
    refreshToken: string;
    idToken: string;
    expiresIn: number;
    tokenType: string;
}

// Helper function to generate JWT auth tokens
export const generateAuthTokens = async (
    userId: string,
    email?: string,
    username?: string,
    firstName?: string,
    lastName?: string,
    displayName?: string,
    authProviders?: string[],
    linkedUsernames?: string[]
): Promise<AuthTokens> => {
    const jwtSecret = await getJWTSecret();
    const now = Math.floor(Date.now() / 1000);
    const accessTokenExpiresIn = 3600; // 1 hour
    const refreshTokenExpiresIn = 30 * 24 * 3600; // 30 days

    const basePayload = {
        sub: userId,
        iat: now,
        email,
        username,
        firstName,
        lastName,
        displayName,
        authProviders,
        linkedUsernames
    };

    const accessToken = jwt.sign({
        ...basePayload,
        exp: now + accessTokenExpiresIn,
        token_use: 'access'
    }, jwtSecret);

    const refreshToken = jwt.sign({
        ...basePayload,
        exp: now + refreshTokenExpiresIn,
        token_use: 'refresh'
    }, jwtSecret);

    const idToken = jwt.sign({
        ...basePayload,
        exp: now + accessTokenExpiresIn,
        token_use: 'id'
    }, jwtSecret);

    return {
        accessToken,
        refreshToken,
        idToken,
        expiresIn: accessTokenExpiresIn,
        tokenType: 'Bearer'
    };
};

// Helper function to verify JWT token
export const verifyToken = async (token: string): Promise<JWTPayload> => {
    try {
        const jwtSecret = await getJWTSecret();
        return jwt.verify(token, jwtSecret) as JWTPayload;
    } catch (error) {
        throw new Error('Invalid token');
    }
};

// Helper function to refresh access token using refresh token
export const refreshAccessToken = async (refreshToken: string): Promise<AuthTokens> => {
    try {
        const jwtSecret = await getJWTSecret();
        const decoded = jwt.verify(refreshToken, jwtSecret) as JWTPayload;

        if (decoded.token_use !== 'refresh') {
            throw new Error('Invalid refresh token');
        }

        // Generate new tokens
        return await generateAuthTokens(
            decoded.sub,
            decoded.email,
            decoded.username,
            decoded.firstName,
            decoded.lastName,
            decoded.displayName,
            decoded.authProviders,
            decoded.linkedUsernames
        );
    } catch (error) {
        throw new Error('Invalid refresh token');
    }
};

// Helper function to gather user context from database for JWT creation
export const gatherUserContext = async (userId: string) => {
    if (!USERS_TABLE || !USER_AUTHS_TABLE) {
        throw new Error('Database tables not configured');
    }

    // Get user record
    const getUserCommand = new GetCommand({
        TableName: USERS_TABLE,
        Key: { id: userId }
    });
    const userResult = await dynamodb.send(getUserCommand);
    const user = userResult.Item;

    if (!user) {
        throw new Error('User not found');
    }

    // Get user auth providers
    const queryAuthsCommand = new QueryCommand({
        TableName: USER_AUTHS_TABLE,
        IndexName: 'UserIdIndex',
        KeyConditionExpression: 'userId = :userId',
        ExpressionAttributeValues: {
            ':userId': userId
        }
    });
    const authsResult = await dynamodb.send(queryAuthsCommand);
    const userAuths = authsResult.Items || [];

    // Extract auth providers and linked usernames
    const authProviders = userAuths.map((auth: any) => auth.provider);
    const linkedUsernames = userAuths
        .filter((auth: any) => auth.username)
        .map((auth: any) => `[${auth.provider}]${auth.username}`);

    return {
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        displayName: user.displayName,
        authProviders,
        linkedUsernames
    };
};

// Helper function to generate JWT tokens with full user context
export const generateAuthTokensWithContext = async (userId: string): Promise<AuthTokens> => {
    const context = await gatherUserContext(userId);
    return generateAuthTokens(
        userId,
        context.email,
        context.username,
        context.firstName,
        context.lastName,
        context.displayName,
        context.authProviders,
        context.linkedUsernames
    );
};

// Helper function to decode token without verification (for debugging)
export const decodeToken = (token: string): JWTPayload | null => {
    try {
        return jwt.decode(token) as JWTPayload;
    } catch (error) {
        return null;
    }
};
