/**
 * Lambda Helper Functions
 * 
 * Common utility functions used across multiple Lambda functions.
 * This centralizes helper functions to avoid code duplication.
 */

import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, QueryCommand } from '@aws-sdk/lib-dynamodb';

// Configure AWS SDK for DynamoDB operations
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

const dynamoClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamoClient);

/**
 * Create standardized API Gateway response
 */
export const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

/**
 * Get user ID from Lambda authorizer context
 */
export const getUserIdFromContext = (event: APIGatewayProxyEvent): string | null => {
    // When using Lambda authorizer, user info is available in the context
    if (event.requestContext && event.requestContext.authorizer) {
        return (event.requestContext.authorizer as any).userId;
    }
    return null;
};

/**
 * Extract request metadata for logging purposes
 */
export const extractRequestMetadata = (event: APIGatewayProxyEvent): { ipAddress?: string; userAgent?: string } => {
    const ipAddress = event.requestContext?.identity?.sourceIp;
    const userAgent = event.headers?.['User-Agent'] || event.headers?.['user-agent'];

    return {
        ipAddress,
        userAgent
    };
};

/**
 * Chunk array into smaller arrays of specified size
 */
export const chunkArray = <T>(array: T[], size: number): T[][] => {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
        chunks.push(array.slice(i, i + size));
    }
    return chunks;
};

/**
 * Validate email format
 */
export const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};

/**
 * Validate UUID format
 */
export const isValidUUID = (uuid: string): boolean => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
};

/**
 * Sanitize string input by removing potentially harmful characters
 */
export const sanitizeString = (input: string): string => {
    return input.replace(/[<>\"'&]/g, '');
};

/**
 * Parse pagination parameters from query string
 */
export const parsePaginationParams = (queryStringParameters: { [key: string]: string } | null): {
    limit: number;
    lastEvaluatedKey?: string;
    offset?: number;
} => {
    const { limit = '10', lastEvaluatedKey, offset } = queryStringParameters || {};

    return {
        limit: Math.min(parseInt(limit) || 10, 100), // Cap at 100
        lastEvaluatedKey,
        offset: offset ? parseInt(offset) : undefined
    };
};

/**
 * Generate a random string of specified length
 */
export const generateRandomString = (length: number): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
};

/**
 * Generate a random username with availability check
 * Follows the pattern: gamer_{randomgen}
 *
 * @param usersTable - DynamoDB table name for users
 * @param maxAttempts - Maximum number of attempts to generate a unique username (default: 10)
 * @returns Promise<string | null> - Generated username or null if failed
 */
export const generateAvailableUsername = async (
    usersTable: string,
    maxAttempts: number = 10
): Promise<string | null> => {
    if (!usersTable) {
        throw new Error('Users table name is required');
    }

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        // Generate random string (6-8 characters for good variety)
        const randomLength = Math.floor(Math.random() * 3) + 6; // 6, 7, or 8 characters
        const randomPart = generateRandomString(randomLength);
        const username = `gamer_${randomPart}`.toLowerCase();

        try {
            // Check if username already exists
            const checkUsernameCommand = new QueryCommand({
                TableName: usersTable,
                IndexName: 'UsernameIndex',
                KeyConditionExpression: 'username = :username',
                ExpressionAttributeValues: {
                    ':username': username
                }
            });

            const existingUserResult = await dynamodb.send(checkUsernameCommand);

            // If no existing user found, username is available
            if (!existingUserResult.Items || existingUserResult.Items.length === 0) {
                console.log(`Generated available username: ${username} (attempt ${attempt})`);
                return username;
            }

            console.log(`Username ${username} already taken, trying again (attempt ${attempt})`);
        } catch (error) {
            console.error(`Error checking username availability for ${username}:`, error);
            // Continue to next attempt on error
        }
    }

    console.error(`Failed to generate available username after ${maxAttempts} attempts`);
    return null;
};

/**
 * Sleep for specified milliseconds
 */
export const sleep = (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Retry function with exponential backoff
 */
export const retryWithBackoff = async <T>(
    fn: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
): Promise<T> => {
    let lastError: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
            return await fn();
        } catch (error) {
            lastError = error as Error;

            if (attempt === maxRetries) {
                throw lastError;
            }

            const delay = baseDelay * Math.pow(2, attempt);
            await sleep(delay);
        }
    }

    throw lastError!;
};

/**
 * Check if a term is an image property (for content filtering)
 */
export const isImageProperty = (term: string): boolean => {
    const imageProperties = [
        'brightness', 'contrast', 'sharpness', 'quality', 'resolution',
        'saturation', 'exposure', 'blur', 'noise', 'grain', 'texture',
        'lighting', 'shadow', 'highlight', 'depth', 'focus', 'clarity'
    ];
    return imageProperties.some(prop => term.toLowerCase().includes(prop));
};

/**
 * Check if a term is a color (for content filtering)
 */
export const isColorTerm = (term: string): boolean => {
    const colors = [
        'red', 'blue', 'green', 'yellow', 'orange', 'purple', 'pink', 'brown',
        'black', 'white', 'gray', 'grey', 'cyan', 'magenta', 'violet', 'indigo',
        'crimson', 'scarlet', 'azure', 'turquoise', 'lime', 'olive', 'navy',
        'maroon', 'teal', 'silver', 'gold', 'beige', 'tan', 'coral'
    ];
    return colors.some(color => term.toLowerCase().includes(color));
};

/**
 * Check if a term is too generic (for content filtering)
 */
export const isGenericTerm = (term: string): boolean => {
    const genericTerms = [
        'image', 'photo', 'picture', 'visual', 'graphic', 'digital',
        'content', 'media', 'file', 'data', 'information', 'item',
        'object', 'thing', 'stuff', 'element', 'component', 'part'
    ];
    return genericTerms.some(generic => term.toLowerCase().includes(generic));
};

/**
 * Validate and normalize phone number
 */
export const normalizePhoneNumber = (phone: string): string | null => {
    // Remove all non-digit characters
    const digits = phone.replace(/\D/g, '');

    // Check if it's a valid US phone number (10 digits) or international (11+ digits)
    if (digits.length === 10) {
        return `+1${digits}`;
    } else if (digits.length === 11 && digits.startsWith('1')) {
        return `+${digits}`;
    } else if (digits.length >= 10) {
        return `+${digits}`;
    }

    return null;
};

/**
 * Calculate time ago string
 */
export const getTimeAgo = (timestamp: string): string => {
    const now = new Date();
    const past = new Date(timestamp);
    const diffMs = now.getTime() - past.getTime();

    const seconds = Math.floor(diffMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    const weeks = Math.floor(days / 7);
    const months = Math.floor(days / 30);
    const years = Math.floor(days / 365);

    if (years > 0) return `${years}y ago`;
    if (months > 0) return `${months}mo ago`;
    if (weeks > 0) return `${weeks}w ago`;
    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'just now';
};

/**
 * Truncate text to specified length with ellipsis
 */
export const truncateText = (text: string, maxLength: number): string => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + '...';
};

/**
 * Deep clone an object
 */
export const deepClone = <T>(obj: T): T => {
    return JSON.parse(JSON.stringify(obj));
};

/**
 * Remove undefined values from object
 */
export const removeUndefined = (obj: Record<string, any>): Record<string, any> => {
    const result: Record<string, any> = {};
    for (const [key, value] of Object.entries(obj)) {
        if (value !== undefined) {
            result[key] = value;
        }
    }
    return result;
};
