{"version": 3, "file": "user-history-service.js", "sourceRoot": "", "sources": ["user-history-service.ts"], "names": [], "mappings": ";;;AAAA,8DAEkC;AAClC,wDAG+B;AAC/B,+BAAoC;AAEpC,6BAA6B;AAC7B,MAAM,cAAc,GAAG,IAAI,gCAAc,CAAC;IACtC,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,WAAW;CAChD,CAAC,CAAC;AACH,MAAM,QAAQ,GAAG,qCAAsB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AAE7D,kCAAkC;AAClC,MAAM,kBAAkB,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC;AAE1D,oBAAoB;AACpB,IAAY,UAmDX;AAnDD,WAAY,UAAU;IAClB,yBAAyB;IACzB,iDAAmC,CAAA;IACnC,2CAA6B,CAAA;IAC7B,mDAAqC,CAAA;IACrC,mEAAqD,CAAA;IACrD,mEAAqD,CAAA;IACrD,mDAAqC,CAAA;IACrC,+CAAiC,CAAA;IAEjC,6BAA6B;IAC7B,2CAA6B,CAAA;IAC7B,2CAA6B,CAAA;IAC7B,yCAA2B,CAAA;IAC3B,yCAA2B,CAAA;IAC3B,yDAA2C,CAAA;IAC3C,2DAA6C,CAAA;IAE7C,kBAAkB;IAClB,iDAAmC,CAAA;IACnC,+CAAiC,CAAA;IACjC,yCAA2B,CAAA;IAE3B,kBAAkB;IAClB,2CAA6B,CAAA;IAC7B,+CAAiC,CAAA;IACjC,2CAA6B,CAAA;IAC7B,yCAA2B,CAAA;IAE3B,iBAAiB;IACjB,+CAAiC,CAAA;IACjC,+CAAiC,CAAA;IAEjC,iBAAiB;IACjB,6CAA+B,CAAA;IAC/B,iDAAmC,CAAA;IACnC,iDAAmC,CAAA;IAEnC,kBAAkB;IAClB,+CAAiC,CAAA;IACjC,2CAA6B,CAAA;IAC7B,iDAAmC,CAAA;IAEnC,mBAAmB;IACnB,6EAA+D,CAAA;IAC/D,mEAAqD,CAAA;IAErD,qBAAqB;IACrB,mDAAqC,CAAA;IACrC,qDAAuC,CAAA;IACvC,yDAA2C,CAAA;AAC/C,CAAC,EAnDW,UAAU,0BAAV,UAAU,QAmDrB;AAmBD,kDAAkD;AAC3C,MAAM,uBAAuB,GAAG,CACnC,MAAc,EACd,MAAkB,EAClB,OAAe,EACf,QAAwC,EACvB,EAAE;IACnB,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;IAC3C,MAAM,MAAM,GAAsB;QAC9B,EAAE,EAAE,IAAA,SAAM,GAAE;QACZ,MAAM;QACN,MAAM;QACN,OAAO;QACP,SAAS;QACT,QAAQ;KACX,CAAC;IAEF,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAjBW,QAAA,uBAAuB,2BAiBlC;AAEF,mCAAmC;AAC5B,MAAM,aAAa,GAAG,KAAK,EAC9B,MAAc,EACd,MAAkB,EAClB,OAAe,EACf,QAAwC,EACxB,EAAE;IAClB,IAAI,CAAC;QACD,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACtB,OAAO,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;YACjE,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,MAAM,GAAG,IAAA,+BAAuB,EAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAE1E,MAAM,UAAU,GAAG,IAAI,yBAAU,CAAC;YAC9B,SAAS,EAAE,kBAAkB;YAC7B,IAAI,EAAE,MAAM;SACf,CAAC,CAAC;QAEH,MAAM,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEhC,OAAO,CAAC,GAAG,CAAC,uBAAuB,MAAM,aAAa,MAAM,EAAE,CAAC,CAAC;QAChE,OAAO,IAAI,CAAC;IAChB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACnD,OAAO,KAAK,CAAC;IACjB,CAAC;AACL,CAAC,CAAC;AA3BW,QAAA,aAAa,iBA2BxB;AAEF,2CAA2C;AAEpC,MAAM,iBAAiB,GAAG,KAAK,EAClC,MAAc,EACd,KAAa,EACb,SAAkB,EAClB,SAAkB,EACF,EAAE;IAClB,OAAO,IAAA,qBAAa,EAChB,MAAM,EACN,UAAU,CAAC,eAAe,EAC1B,+BAA+B,KAAK,EAAE,EACtC;QACI,WAAW,EAAE,IAAI;QACjB,UAAU,EAAE,EAAE,KAAK,EAAE;QACrB,SAAS;QACT,SAAS;KACZ,CACJ,CAAC;AACN,CAAC,CAAC;AAjBW,QAAA,iBAAiB,qBAiB5B;AAEK,MAAM,cAAc,GAAG,KAAK,EAC/B,MAAc,EACd,QAAgB,EAChB,SAAkB,EAClB,SAAkB,EACF,EAAE;IAClB,OAAO,IAAA,qBAAa,EAChB,MAAM,EACN,UAAU,CAAC,YAAY,EACvB,oBAAoB,QAAQ,EAAE,EAC9B;QACI,WAAW,EAAE,IAAI;QACjB,UAAU,EAAE,QAAQ;QACpB,SAAS;QACT,SAAS;KACZ,CACJ,CAAC;AACN,CAAC,CAAC;AAjBW,QAAA,cAAc,kBAiBzB;AAEK,MAAM,kBAAkB,GAAG,KAAK,EACnC,MAAc,EACd,WAAmB,EACnB,WAAmB,EACnB,SAAkB,EAClB,SAAkB,EACF,EAAE;IAClB,OAAO,IAAA,qBAAa,EAChB,MAAM,EACN,UAAU,CAAC,gBAAgB,EAC3B,0BAA0B,WAAW,SAAS,WAAW,GAAG,EAC5D;QACI,WAAW,EAAE,WAAW;QACxB,UAAU,EAAE,WAAW;QACvB,SAAS;QACT,SAAS;KACZ,CACJ,CAAC;AACN,CAAC,CAAC;AAlBW,QAAA,kBAAkB,sBAkB7B;AAEK,MAAM,yBAAyB,GAAG,KAAK,EAC1C,MAAc,EACd,KAAa,EACb,SAAkB,EAClB,SAAkB,EACF,EAAE;IAClB,OAAO,IAAA,qBAAa,EAChB,MAAM,EACN,UAAU,CAAC,wBAAwB,EACnC,uCAAuC,KAAK,EAAE,EAC9C;QACI,SAAS;QACT,SAAS;KACZ,CACJ,CAAC;AACN,CAAC,CAAC;AAfW,QAAA,yBAAyB,6BAepC;AAEK,MAAM,yBAAyB,GAAG,KAAK,EAC1C,MAAc,EACd,SAAkB,EAClB,SAAkB,EACF,EAAE;IAClB,OAAO,IAAA,qBAAa,EAChB,MAAM,EACN,UAAU,CAAC,wBAAwB,EACnC,uCAAuC,EACvC;QACI,SAAS;QACT,SAAS;KACZ,CACJ,CAAC;AACN,CAAC,CAAC;AAdW,QAAA,yBAAyB,6BAcpC;AAEK,MAAM,cAAc,GAAG,KAAK,EAC/B,MAAc,EACd,MAAc,EACd,KAAc,EACd,SAAkB,EACF,EAAE;IAClB,MAAM,OAAO,GAAG,KAAK;QACjB,CAAC,CAAC,kBAAkB,KAAK,UAAU,MAAM,GAAG;QAC5C,CAAC,CAAC,yBAAyB,MAAM,EAAE,CAAC;IAExC,OAAO,IAAA,qBAAa,EAChB,MAAM,EACN,UAAU,CAAC,YAAY,EACvB,OAAO,EACP;QACI,WAAW,EAAE,MAAM;QACnB,cAAc,EAAE;YACZ,KAAK;YACL,SAAS;SACZ;KACJ,CACJ,CAAC;AACN,CAAC,CAAC;AAtBW,QAAA,cAAc,kBAsBzB;AAEK,MAAM,gBAAgB,GAAG,KAAK,EACjC,MAAc,EACd,MAAc,EACd,KAAc,EACE,EAAE;IAClB,MAAM,OAAO,GAAG,KAAK;QACjB,CAAC,CAAC,oBAAoB,KAAK,UAAU,MAAM,GAAG;QAC9C,CAAC,CAAC,2BAA2B,MAAM,EAAE,CAAC;IAE1C,OAAO,IAAA,qBAAa,EAChB,MAAM,EACN,UAAU,CAAC,cAAc,EACzB,OAAO,EACP;QACI,WAAW,EAAE,MAAM;QACnB,cAAc,EAAE,EAAE,KAAK,EAAE;KAC5B,CACJ,CAAC;AACN,CAAC,CAAC;AAlBW,QAAA,gBAAgB,oBAkB3B;AAEK,MAAM,gBAAgB,GAAG,KAAK,EACjC,MAAc,EACd,QAAgB,EAChB,MAAc,EACd,UAAkB,EACF,EAAE;IAClB,OAAO,IAAA,qBAAa,EAChB,MAAM,EACN,UAAU,CAAC,cAAc,EACzB,GAAG,UAAU,2BAA2B,MAAM,EAAE,EAChD;QACI,WAAW,EAAE,QAAQ;QACrB,cAAc,EAAE;YACZ,MAAM;YACN,UAAU;SACb;KACJ,CACJ,CAAC;AACN,CAAC,CAAC;AAlBW,QAAA,gBAAgB,oBAkB3B;AAEK,MAAM,iBAAiB,GAAG,KAAK,EAClC,MAAc,EACd,OAA4B,EAC5B,SAAkB,EAClB,SAAkB,EACF,EAAE;IAClB,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEtD,OAAO,IAAA,qBAAa,EAChB,MAAM,EACN,UAAU,CAAC,eAAe,EAC1B,oBAAoB,aAAa,EAAE,EACnC;QACI,WAAW,EAAE,OAAO,CAAC,MAAM,IAAI,IAAI;QACnC,UAAU,EAAE,OAAO,CAAC,KAAK,IAAI,IAAI;QACjC,SAAS;QACT,SAAS;QACT,cAAc,EAAE,EAAE,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;KAC1D,CACJ,CAAC;AACN,CAAC,CAAC;AApBW,QAAA,iBAAiB,qBAoB5B;AAEK,MAAM,cAAc,GAAG,KAAK,EAC/B,MAAc,EACd,KAAc,EACd,SAAkB,EAClB,SAAkB,EACF,EAAE;IAClB,OAAO,IAAA,qBAAa,EAChB,MAAM,EACN,UAAU,CAAC,YAAY,EACvB,uBAAuB,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EACnD;QACI,WAAW,EAAE,IAAI;QACjB,UAAU,EAAE,EAAE,KAAK,EAAE;QACrB,SAAS;QACT,SAAS;KACZ,CACJ,CAAC;AACN,CAAC,CAAC;AAjBW,QAAA,cAAc,kBAiBzB;AAEK,MAAM,cAAc,GAAG,KAAK,EAC/B,MAAc,EACd,KAAc,EACd,SAAkB,EAClB,SAAkB,EACF,EAAE;IAClB,OAAO,IAAA,qBAAa,EAChB,MAAM,EACN,UAAU,CAAC,YAAY,EACvB,uBAAuB,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EACnD;QACI,WAAW,EAAE,IAAI;QACjB,UAAU,EAAE,EAAE,KAAK,EAAE;QACrB,SAAS;QACT,SAAS;KACZ,CACJ,CAAC;AACN,CAAC,CAAC;AAjBW,QAAA,cAAc,kBAiBzB;AAEK,MAAM,aAAa,GAAG,KAAK,EAC9B,MAAc,EACd,UAAmB,EACnB,QAAiB,EACjB,SAAkB,EAClB,SAAkB,EACF,EAAE;IAClB,OAAO,IAAA,qBAAa,EAChB,MAAM,EACN,UAAU,CAAC,WAAW,EACtB,sBAAsB,QAAQ,CAAC,CAAC,CAAC,KAAK,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EACxD;QACI,WAAW,EAAE,IAAI;QACjB,UAAU,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE;QACpC,SAAS;QACT,SAAS;KACZ,CACJ,CAAC;AACN,CAAC,CAAC;AAlBW,QAAA,aAAa,iBAkBxB;AAEK,MAAM,aAAa,GAAG,KAAK,EAC9B,MAAc,EACd,UAAmB,EACnB,QAAiB,EACjB,SAAkB,EAClB,SAAkB,EACF,EAAE;IAClB,OAAO,IAAA,qBAAa,EAChB,MAAM,EACN,UAAU,CAAC,WAAW,EACtB,sBAAsB,QAAQ,CAAC,CAAC,CAAC,KAAK,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EACxD;QACI,WAAW,EAAE,IAAI;QACjB,UAAU,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE;QACpC,SAAS;QACT,SAAS;KACZ,CACJ,CAAC;AACN,CAAC,CAAC;AAlBW,QAAA,aAAa,iBAkBxB;AAEK,MAAM,qBAAqB,GAAG,KAAK,EACtC,MAAc,EACd,KAAc,EACd,SAAkB,EAClB,SAAkB,EACF,EAAE;IAClB,OAAO,IAAA,qBAAa,EAChB,MAAM,EACN,UAAU,CAAC,oBAAoB,EAC/B,uBAAuB,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EACnD;QACI,WAAW,EAAE,IAAI;QACjB,UAAU,EAAE,EAAE,KAAK,EAAE;QACrB,SAAS;QACT,SAAS;KACZ,CACJ,CAAC;AACN,CAAC,CAAC;AAjBW,QAAA,qBAAqB,yBAiBhC;AAEK,MAAM,oBAAoB,GAAG,KAAK,EACrC,MAAc,EACd,UAAmB,EACnB,QAAiB,EACjB,SAAkB,EAClB,SAAkB,EACF,EAAE;IAClB,OAAO,IAAA,qBAAa,EAChB,MAAM,EACN,UAAU,CAAC,mBAAmB,EAC9B,sBAAsB,QAAQ,CAAC,CAAC,CAAC,KAAK,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EACxD;QACI,WAAW,EAAE,IAAI;QACjB,UAAU,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE;QACpC,SAAS;QACT,SAAS;KACZ,CACJ,CAAC;AACN,CAAC,CAAC;AAlBW,QAAA,oBAAoB,wBAkB/B;AAEF,8EAA8E;AACvE,MAAM,sBAAsB,GAAG,CAAC,KAAU,EAAE,EAAE;IACjD,OAAO;QACH,SAAS,EAAE,KAAK,CAAC,cAAc,EAAE,QAAQ,EAAE,QAAQ;YAC/C,KAAK,CAAC,OAAO,EAAE,CAAC,iBAAiB,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE;YACzD,KAAK,CAAC,OAAO,EAAE,CAAC,iBAAiB,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE;QAC7D,SAAS,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC,YAAY,CAAC;KAC5E,CAAC;AACN,CAAC,CAAC;AAPW,QAAA,sBAAsB,0BAOjC"}