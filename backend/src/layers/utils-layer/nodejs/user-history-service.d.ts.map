{"version": 3, "file": "user-history-service.d.ts", "sourceRoot": "", "sources": ["user-history-service.ts"], "names": [], "mappings": "AAmBA,oBAAY,UAAU;IAElB,eAAe,oBAAoB;IACnC,YAAY,iBAAiB;IAC7B,gBAAgB,qBAAqB;IACrC,wBAAwB,6BAA6B;IACrD,wBAAwB,6BAA6B;IACrD,gBAAgB,qBAAqB;IACrC,cAAc,mBAAmB;IAGjC,YAAY,iBAAiB;IAC7B,YAAY,iBAAiB;IAC7B,WAAW,gBAAgB;IAC3B,WAAW,gBAAgB;IAC3B,mBAAmB,wBAAwB;IAC3C,oBAAoB,yBAAyB;IAG7C,eAAe,oBAAoB;IACnC,cAAc,mBAAmB;IACjC,WAAW,gBAAgB;IAG3B,YAAY,iBAAiB;IAC7B,cAAc,mBAAmB;IACjC,YAAY,iBAAiB;IAC7B,WAAW,gBAAgB;IAG3B,cAAc,mBAAmB;IACjC,cAAc,mBAAmB;IAGjC,aAAa,kBAAkB;IAC/B,eAAe,oBAAoB;IACnC,eAAe,oBAAoB;IAGnC,cAAc,mBAAmB;IACjC,YAAY,iBAAiB;IAC7B,eAAe,oBAAoB;IAGnC,6BAA6B,kCAAkC;IAC/D,wBAAwB,6BAA6B;IAGrD,gBAAgB,qBAAqB;IACrC,iBAAiB,sBAAsB;IACvC,mBAAmB,wBAAwB;CAC9C;AAGD,MAAM,WAAW,iBAAiB;IAC9B,EAAE,EAAE,MAAM,CAAC;IACX,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,UAAU,CAAC;IACnB,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,MAAM,CAAC;IAClB,QAAQ,CAAC,EAAE;QACP,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,WAAW,CAAC,EAAE,GAAG,CAAC;QAClB,UAAU,CAAC,EAAE,GAAG,CAAC;QACjB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,cAAc,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;KACxC,CAAC;CACL;AAGD,eAAO,MAAM,uBAAuB,WACxB,MAAM,UACN,UAAU,WACT,MAAM,aACJ,iBAAiB,CAAC,UAAU,CAAC,KACzC,iBAYF,CAAC;AAGF,eAAO,MAAM,aAAa,WACd,MAAM,UACN,UAAU,WACT,MAAM,aACJ,iBAAiB,CAAC,UAAU,CAAC,KACzC,OAAO,CAAC,OAAO,CAsBjB,CAAC;AAIF,eAAO,MAAM,iBAAiB,WAClB,MAAM,SACP,MAAM,cACD,MAAM,cACN,MAAM,KACnB,OAAO,CAAC,OAAO,CAYjB,CAAC;AAEF,eAAO,MAAM,cAAc,WACf,MAAM,YACJ,MAAM,cACJ,MAAM,cACN,MAAM,KACnB,OAAO,CAAC,OAAO,CAYjB,CAAC;AAEF,eAAO,MAAM,kBAAkB,WACnB,MAAM,eACD,MAAM,eACN,MAAM,cACP,MAAM,cACN,MAAM,KACnB,OAAO,CAAC,OAAO,CAYjB,CAAC;AAEF,eAAO,MAAM,yBAAyB,WAC1B,MAAM,SACP,MAAM,cACD,MAAM,cACN,MAAM,KACnB,OAAO,CAAC,OAAO,CAUjB,CAAC;AAEF,eAAO,MAAM,yBAAyB,WAC1B,MAAM,cACF,MAAM,cACN,MAAM,KACnB,OAAO,CAAC,OAAO,CAUjB,CAAC;AAEF,eAAO,MAAM,cAAc,WACf,MAAM,UACN,MAAM,UACN,MAAM,cACF,MAAM,KACnB,OAAO,CAAC,OAAO,CAiBjB,CAAC;AAEF,eAAO,MAAM,gBAAgB,WACjB,MAAM,UACN,MAAM,UACN,MAAM,KACf,OAAO,CAAC,OAAO,CAcjB,CAAC;AAEF,eAAO,MAAM,gBAAgB,WACjB,MAAM,YACJ,MAAM,UACR,MAAM,cACF,MAAM,KACnB,OAAO,CAAC,OAAO,CAajB,CAAC;AAEF,eAAO,MAAM,iBAAiB,WAClB,MAAM,WACL,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,cAChB,MAAM,cACN,MAAM,KACnB,OAAO,CAAC,OAAO,CAejB,CAAC;AAEF,eAAO,MAAM,cAAc,WACf,MAAM,UACN,MAAM,cACF,MAAM,cACN,MAAM,KACnB,OAAO,CAAC,OAAO,CAYjB,CAAC;AAEF,eAAO,MAAM,cAAc,WACf,MAAM,UACN,MAAM,cACF,MAAM,cACN,MAAM,KACnB,OAAO,CAAC,OAAO,CAYjB,CAAC;AAEF,eAAO,MAAM,aAAa,WACd,MAAM,eACD,MAAM,aACR,MAAM,cACL,MAAM,cACN,MAAM,KACnB,OAAO,CAAC,OAAO,CAYjB,CAAC;AAEF,eAAO,MAAM,aAAa,WACd,MAAM,eACD,MAAM,aACR,MAAM,cACL,MAAM,cACN,MAAM,KACnB,OAAO,CAAC,OAAO,CAYjB,CAAC;AAEF,eAAO,MAAM,qBAAqB,WACtB,MAAM,UACN,MAAM,cACF,MAAM,cACN,MAAM,KACnB,OAAO,CAAC,OAAO,CAYjB,CAAC;AAEF,eAAO,MAAM,oBAAoB,WACrB,MAAM,eACD,MAAM,aACR,MAAM,cACL,MAAM,cACN,MAAM,KACnB,OAAO,CAAC,OAAO,CAYjB,CAAC;AAGF,eAAO,MAAM,sBAAsB,UAAW,GAAG;;;CAOhD,CAAC"}