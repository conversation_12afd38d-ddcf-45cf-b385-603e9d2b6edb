/**
 * UserAuth Service Layer
 * 
 * Centralized service for managing user authentication data across all providers
 */

import {
    DynamoDBClient
} from '@aws-sdk/client-dynamodb';
import {
    DynamoDBDocumentClient,
    PutCommand,
    GetCommand,
    UpdateCommand,
    DeleteCommand,
    QueryCommand,
    type QueryCommandInput
} from '@aws-sdk/lib-dynamodb';
import { v4 as uuidv4 } from 'uuid';

import {
    UserAuth,
    AuthProvider,
    CreateUserAuthRequest,
    UpdateUserAuthRequest,
    FindUserAuthByProviderRequest,
    FindUserAuthsByUserIdRequest,
    USER_AUTH_TABLE_INDEXES,
    isValidAuthProvider
} from './user-auth';

// AWS Configuration
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

const dynamoClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamoClient);

// Environment variables
const USER_AUTHS_TABLE = process.env.USER_AUTHS_TABLE || process.env.USERAUTHS_TABLE_NAME;

/**
 * UserAuth Service Class
 */
export class UserAuthService {
    private tableName: string;

    constructor(tableName?: string) {
        this.tableName = tableName || USER_AUTHS_TABLE || '';
        if (!this.tableName) {
            throw new Error('UserAuths table name not configured');
        }
    }

    /**
     * Create a new user authentication record
     */
    async createUserAuth(request: CreateUserAuthRequest): Promise<UserAuth> {
        const now = new Date().toISOString();
        const userAuth: UserAuth = {
            id: uuidv4(),
            userId: request.userId,
            provider: request.provider,
            providerUserId: request.providerUserId,
            email: request.email ? request.email?.toLowerCase() : undefined, // Normalize email
            username: request.username,
            accessToken: request.accessToken,
            refreshToken: request.refreshToken,
            idToken: request.idToken,
            tokenExpiresAt: request.tokenExpiresAt,
            providerData: request.providerData,
            isActive: true,
            isPrimary: request.isPrimary || false,
            isVerified: request.isVerified || false,
            createdAt: now,
            updatedAt: now
        };

        const command = new PutCommand({
            TableName: this.tableName,
            Item: userAuth
        });

        await dynamodb.send(command);
        return userAuth;
    }

    /**
     * Get user authentication by ID
     */
    async getUserAuthById(id: string): Promise<UserAuth | null> {
        const command = new GetCommand({
            TableName: this.tableName,
            Key: { id }
        });

        const result = await dynamodb.send(command);
        return result.Item as UserAuth || null;
    }

    /**
     * Update user authentication record
     */
    async updateUserAuth(request: UpdateUserAuthRequest): Promise<UserAuth | null> {
        const now = new Date().toISOString();

        // Build update expression dynamically
        const updateExpressions: string[] = [];
        const expressionAttributeNames: Record<string, string> = {};
        const expressionAttributeValues: Record<string, any> = {};

        if (request.username !== undefined) {
            updateExpressions.push('#username = :username');
            expressionAttributeNames['#username'] = 'username';
            expressionAttributeValues[':username'] = request.username;
        }

        if (request.accessToken !== undefined) {
            updateExpressions.push('#accessToken = :accessToken');
            expressionAttributeNames['#accessToken'] = 'accessToken';
            expressionAttributeValues[':accessToken'] = request.accessToken;
        }

        if (request.refreshToken !== undefined) {
            updateExpressions.push('#refreshToken = :refreshToken');
            expressionAttributeNames['#refreshToken'] = 'refreshToken';
            expressionAttributeValues[':refreshToken'] = request.refreshToken;
        }

        if (request.idToken !== undefined) {
            updateExpressions.push('#idToken = :idToken');
            expressionAttributeNames['#idToken'] = 'idToken';
            expressionAttributeValues[':idToken'] = request.idToken;
        }

        if (request.tokenExpiresAt !== undefined) {
            updateExpressions.push('#tokenExpiresAt = :tokenExpiresAt');
            expressionAttributeNames['#tokenExpiresAt'] = 'tokenExpiresAt';
            expressionAttributeValues[':tokenExpiresAt'] = request.tokenExpiresAt;
        }

        if (request.providerData !== undefined) {
            updateExpressions.push('#providerData = :providerData');
            expressionAttributeNames['#providerData'] = 'providerData';
            expressionAttributeValues[':providerData'] = request.providerData;
        }

        if (request.isActive !== undefined) {
            updateExpressions.push('#isActive = :isActive');
            expressionAttributeNames['#isActive'] = 'isActive';
            expressionAttributeValues[':isActive'] = request.isActive;
        }

        if (request.isPrimary !== undefined) {
            updateExpressions.push('#isPrimary = :isPrimary');
            expressionAttributeNames['#isPrimary'] = 'isPrimary';
            expressionAttributeValues[':isPrimary'] = request.isPrimary;
        }

        if (request.isVerified !== undefined) {
            updateExpressions.push('#isVerified = :isVerified');
            expressionAttributeNames['#isVerified'] = 'isVerified';
            expressionAttributeValues[':isVerified'] = request.isVerified;
        }

        if (request.lastUsedAt !== undefined) {
            updateExpressions.push('#lastUsedAt = :lastUsedAt');
            expressionAttributeNames['#lastUsedAt'] = 'lastUsedAt';
            expressionAttributeValues[':lastUsedAt'] = request.lastUsedAt;
        }

        // Always update the updatedAt timestamp
        updateExpressions.push('#updatedAt = :updatedAt');
        expressionAttributeNames['#updatedAt'] = 'updatedAt';
        expressionAttributeValues[':updatedAt'] = now;

        if (updateExpressions.length === 1) {
            // Only updatedAt was set, nothing to update
            return await this.getUserAuthById(request.id);
        }

        const command = new UpdateCommand({
            TableName: this.tableName,
            Key: { id: request.id },
            UpdateExpression: `SET ${updateExpressions.join(', ')}`,
            ExpressionAttributeNames: expressionAttributeNames,
            ExpressionAttributeValues: expressionAttributeValues,
            ReturnValues: 'ALL_NEW'
        });

        const result = await dynamodb.send(command);
        return result.Attributes as UserAuth || null;
    }

    /**
     * Delete user authentication record
     */
    async deleteUserAuth(id: string): Promise<boolean> {
        const command = new DeleteCommand({
            TableName: this.tableName,
            Key: { id }
        });

        await dynamodb.send(command);
        return true;
    }

    /**
     * Find user authentication by provider and provider-specific identifier
     */
    async findUserAuthByProvider(request: FindUserAuthByProviderRequest): Promise<UserAuth | null> {
        if (request.email) {
            // Search by email
            const command = new QueryCommand({
                TableName: this.tableName,
                IndexName: USER_AUTH_TABLE_INDEXES.EMAIL_INDEX,
                KeyConditionExpression: 'email = :email',
                FilterExpression: 'provider = :provider AND isActive = :isActive',
                ExpressionAttributeValues: {
                    ':email': request.email.toLowerCase(),
                    ':provider': request.provider,
                    ':isActive': true
                }
            });

            const result = await dynamodb.send(command);
            return result.Items?.[0] as UserAuth || null;
        }

        if (request.providerUserId) {
            // Search by provider + providerUserId
            const command = new QueryCommand({
                TableName: this.tableName,
                IndexName: USER_AUTH_TABLE_INDEXES.PROVIDER_USER_ID_INDEX,
                KeyConditionExpression: 'provider = :provider AND providerUserId = :providerUserId',
                FilterExpression: 'isActive = :isActive',
                ExpressionAttributeValues: {
                    ':provider': request.provider,
                    ':providerUserId': request.providerUserId,
                    ':isActive': true
                }
            });

            const result = await dynamodb.send(command);
            return result.Items?.[0] as UserAuth || null;
        }

        throw new Error('Either email or providerUserId must be provided');
    }

    /**
     * Find user authentication by username (e.g., gamertag, Twitch username)
     */
    async findUserAuthByUsername(username: string): Promise<UserAuth[]> {
        const command = new QueryCommand({
            TableName: this.tableName,
            IndexName: USER_AUTH_TABLE_INDEXES.USERNAME_INDEX,
            KeyConditionExpression: 'username = :username',
            FilterExpression: 'isActive = :isActive',
            ExpressionAttributeValues: {
                ':username': username,
                ':isActive': true
            }
        });

        const result = await dynamodb.send(command);
        return result.Items as UserAuth[] || [];
    }

    /**
     * Find all user authentications for a specific user
     */
    async findUserAuthsByUserId(request: FindUserAuthsByUserIdRequest): Promise<UserAuth[]> {
        const queryInput: QueryCommandInput = {
            TableName: this.tableName,
            IndexName: USER_AUTH_TABLE_INDEXES.USER_ID_INDEX,
            KeyConditionExpression: 'userId = :userId',
            ExpressionAttributeValues: {
                ':userId': request.userId
            }
        };

        if (request.activeOnly) {
            queryInput.FilterExpression = 'isActive = :isActive';
            queryInput.ExpressionAttributeValues = {
                ...queryInput.ExpressionAttributeValues,
                ':isActive': true
            };
        }

        const command = new QueryCommand(queryInput);
        const result = await dynamodb.send(command);
        return result.Items as UserAuth[] || [];
    }

    /**
     * Set a user auth as primary (and unset others)
     */
    async setPrimaryUserAuth(userId: string, authId: string): Promise<boolean> {
        // First, get all user auths for this user
        const userAuths = await this.findUserAuthsByUserId({ userId, activeOnly: true });

        // Update all auths to not be primary, then set the specified one as primary
        const updatePromises = userAuths.map(auth => {
            return this.updateUserAuth({
                id: auth.id,
                isPrimary: auth.id === authId
            });
        });

        await Promise.all(updatePromises);
        return true;
    }

    /**
     * Mark user auth as used (update lastUsedAt)
     */
    async markUserAuthAsUsed(id: string): Promise<void> {
        await this.updateUserAuth({
            id,
            lastUsedAt: new Date().toISOString()
        });
    }

    /**
     * Get all available usernames for a user (from all their auth providers)
     */
    async getUserAvailableUsernames(userId: string): Promise<Array<{ provider: AuthProvider, username: string }>> {
        const userAuths = await this.findUserAuthsByUserId({ userId, activeOnly: true });

        return userAuths
            .filter(auth => auth.username && auth.username.trim() !== '')
            .map(auth => ({
                provider: auth.provider,
                username: auth.username!
            }));
    }
}

// Export singleton instance
export const userAuthService = new UserAuthService();
