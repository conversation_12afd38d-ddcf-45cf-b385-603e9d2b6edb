import {
    CognitoIdentityProviderClient,
    AdminInitiateAuthCommand,
    type AdminInitiateAuthCommandInput,
    type AuthFlowType
} from '@aws-sdk/client-cognito-identity-provider';
import {
    DynamoDBClient
} from '@aws-sdk/client-dynamodb';
import {
    DynamoDBDocumentClient,
    GetCommand
} from '@aws-sdk/lib-dynamodb';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { UserAuthService } from '/opt/nodejs/user-auth-service';
import { AuthProvider } from '/opt/nodejs/user-auth';
import { generateAuthTokensWithContext } from '/opt/nodejs/jwt-auth-service';

// Types
interface SignInRequest {
    email: string;
    password: string;
}

interface UserRecord {
    id: string;
    email?: string; // Now optional - stored in UserAuths table
    username?: string;
    createdAt: string;
    updatedAt: string;
}

// Configure AWS SDK
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

if (process.env.AWS_SAM_LOCAL) {
    console.log('Running in SAM Local mode');
}

const cognitoClient = new CognitoIdentityProviderClient(awsConfig);
const dynamoClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamoClient);

// Environment variables
const USER_POOL_ID = process.env.USER_POOL_ID || process.env.USERPOOL_USER_POOL_ID;
const USER_POOL_CLIENT_ID = process.env.USER_POOL_CLIENT_ID || process.env.USERPOOLCLIENT_USER_POOL_CLIENT_ID;
const USERS_TABLE = process.env.USERS_TABLE || process.env.USERSTABLE_TABLE_NAME;
const USER_AUTHS_TABLE = process.env.USER_AUTHS_TABLE || process.env.USERAUTHS_TABLE_NAME;

// Initialize UserAuth service
const userAuthService = new UserAuthService(USER_AUTHS_TABLE);

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Sign in handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('SignIn Event:', JSON.stringify(event, null, 2));

    // Handle CORS preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return createResponse(200, {});
    }

    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { email, password }: SignInRequest = JSON.parse(event.body);

        if (!email || !password) {
            return createResponse(400, { error: 'Email and password are required' });
        }

        // Normalize email to lowercase for case-insensitive handling
        const normalizedEmail = email.toLowerCase();

        if (!USER_POOL_ID || !USER_POOL_CLIENT_ID) {
            return createResponse(500, { error: 'Cognito configuration missing' });
        }

        // Authenticate with Cognito
        const authParams: AdminInitiateAuthCommandInput = {
            UserPoolId: USER_POOL_ID,
            ClientId: USER_POOL_CLIENT_ID,
            AuthFlow: 'ADMIN_USER_PASSWORD_AUTH' as AuthFlowType,
            AuthParameters: {
                USERNAME: normalizedEmail,
                PASSWORD: password
            }
        };

        const authCommand = new AdminInitiateAuthCommand(authParams);
        const authResult = await cognitoClient.send(authCommand);

        if (!USERS_TABLE) {
            return createResponse(500, { error: 'Users table configuration missing' });
        }

        // Find user authentication by email (CognitoID is stored as providerUserId)
        const userAuth = await userAuthService.findUserAuthByProvider({
            provider: AuthProvider.EMAIL,
            email: normalizedEmail
        });

        if (!userAuth) {
            return createResponse(404, { error: 'User not found' });
        }

        // Verify that the Cognito username matches what we have stored
        if (userAuth.providerUserId !== normalizedEmail) {
            return createResponse(401, { error: 'Authentication failed - user mismatch' });
        }

        // Mark this auth method as used
        await userAuthService.markUserAuthAsUsed(userAuth.id);

        // Get user details from DynamoDB
        const getUserCommand = new GetCommand({
            TableName: USERS_TABLE,
            Key: { id: userAuth.userId }
        });
        const userResult = await dynamodb.send(getUserCommand);

        if (!userResult.Item) {
            return createResponse(404, { error: 'User record not found' });
        }

        const user = userResult.Item as UserRecord;

        if (!authResult.AuthenticationResult) {
            return createResponse(401, { error: 'Authentication failed' });
        }

        // Generate JWT tokens with user context
        const jwtTokens = await generateAuthTokensWithContext(user.id);

        // Check if user has a username set
        if (!user.username) {
            return createResponse(200, {
                message: 'Sign in successful',
                requiresUsername: true,
                tokens: {
                    accessToken: jwtTokens.accessToken,
                    refreshToken: jwtTokens.refreshToken,
                    idToken: jwtTokens.idToken
                },
                user: {
                    id: user.id,
                    email: userAuth.email
                }
            });
        }

        return createResponse(200, {
            message: 'Sign in successful',
            tokens: {
                accessToken: jwtTokens.accessToken,
                refreshToken: jwtTokens.refreshToken,
                idToken: jwtTokens.idToken
            },
            user: {
                id: user.id,
                email: userAuth.email,
                username: user.username
            }
        });

    } catch (error: any) {
        console.error('SignIn error:', error);

        // Handle specific Cognito errors
        if (error.name === 'NotAuthorizedException') {
            return createResponse(401, {
                error: 'Invalid email or password',
                details: 'The email or password you entered is incorrect.'
            });
        } else if (error.name === 'UserNotConfirmedException') {
            return createResponse(401, {
                error: 'Email not confirmed',
                details: 'Please check your email and confirm your account before signing in.'
            });
        } else if (error.name === 'UserNotFoundException') {
            return createResponse(401, {
                error: 'Invalid email or password',
                details: 'The email or password you entered is incorrect.'
            });
        } else if (error.name === 'TooManyRequestsException') {
            return createResponse(429, {
                error: 'Too many attempts',
                details: 'Too many failed sign-in attempts. Please try again later.'
            });
        } else if (error.name === 'InvalidParameterException') {
            return createResponse(400, {
                error: 'Invalid request',
                details: 'Please check your email and password format.'
            });
        } else {
            return createResponse(500, {
                error: 'Authentication failed',
                details: 'An unexpected error occurred. Please try again.'
            });
        }
    }
};
