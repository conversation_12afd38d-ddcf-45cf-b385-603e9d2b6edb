import {
    CognitoIdentityProviderClient,
    SignUpCommand,
    AdminConfirmSignUpCommand,
    type SignUpCommandInput
} from '@aws-sdk/client-cognito-identity-provider';
import {
    DynamoDBClient
} from '@aws-sdk/client-dynamodb';
import {
    DynamoDBDocumentClient,
    PutCommand
} from '@aws-sdk/lib-dynamodb';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import * as emailValidator from 'email-validator';
const disposableDomains: string[] = require('disposable-email-domains');
import { logAccountCreated, extractRequestMetadata } from '/opt/nodejs/user-history-service';

import { createResponse, generateAvailableUsername } from '/opt/nodejs/lambda-helpers';
import { UserAuthService } from '/opt/nodejs/user-auth-service';
import { AuthProvider } from '/opt/nodejs/user-auth';

// Types
interface SignUpRequest {
    email: string;
    password: string;
}

interface UserRecord {
    id: string;
    email?: string; // Now optional - stored in UserAuths table
    username?: string;
    displayName?: string; // Always includes provider prefix like "[xbox]Gamertag123"
    createdAt: string;
    updatedAt: string;
}

// Configure AWS SDK
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

const cognitoClient = new CognitoIdentityProviderClient(awsConfig);
const dynamoClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamoClient);

// Environment variables
const USER_POOL_ID = process.env.USER_POOL_ID || process.env.USERPOOL_USER_POOL_ID;
const USER_POOL_CLIENT_ID = process.env.USER_POOL_CLIENT_ID || process.env.USERPOOLCLIENT_USER_POOL_CLIENT_ID;
const USERS_TABLE = process.env.USERS_TABLE || process.env.USERSTABLE_TABLE_NAME;
const USER_AUTHS_TABLE = process.env.USER_AUTHS_TABLE || process.env.USERAUTHS_TABLE_NAME;

// Initialize UserAuth service
const userAuthService = new UserAuthService(USER_AUTHS_TABLE);



// Sign up handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('SignUp Event:', JSON.stringify(event, null, 2));

    // Handle CORS preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return createResponse(200, {});
    }

    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { email, password }: SignUpRequest = JSON.parse(event.body);

        if (!email || !password) {
            return createResponse(400, { error: 'Email and password are required' });
        }

        // Validate email format
        if (!emailValidator.validate(email)) {
            return createResponse(400, {
                error: 'Invalid email format',
                details: 'Please enter a valid email address.'
            });
        }

        // Normalize email to lowercase for case-insensitive handling
        const normalizedEmail = email.toLowerCase();

        // Extract domain from email
        const emailDomain = normalizedEmail.split('@')[1];

        // List of additional blocked email domains (case-insensitive)
        const additionalBlockedDomains = [
            'gameflex.temp'
        ];

        // Check if email domain is disposable or blocked
        if (disposableDomains.includes(emailDomain) || additionalBlockedDomains.includes(emailDomain)) {
            return createResponse(400, {
                error: 'Invalid email domain',
                details: 'Disposable email addresses and certain domains are not allowed for registration. Please use a permanent email address.'
            });
        }

        if (!USER_POOL_ID || !USER_POOL_CLIENT_ID) {
            return createResponse(500, { error: 'User pool configuration missing' });
        }

        // Use SignUp command instead of AdminCreateUser to trigger email verification
        const signUpParams: SignUpCommandInput = {
            ClientId: USER_POOL_CLIENT_ID,
            Username: normalizedEmail,
            Password: password,
            UserAttributes: [
                { Name: 'email', Value: normalizedEmail }
            ]
        };

        const signUpCommand = new SignUpCommand(signUpParams);
        const signUpResult = await cognitoClient.send(signUpCommand);

        console.log('SignUp result:', signUpResult);

        // Check if email verification is required based on environment
        const isDevelopment = process.env.ENVIRONMENT === 'development';
        const requiresVerification = !isDevelopment;

        // Auto-confirm user in development environment
        if (isDevelopment) {
            try {
                const confirmCommand = new AdminConfirmSignUpCommand({
                    UserPoolId: USER_POOL_ID,
                    Username: normalizedEmail
                });
                await cognitoClient.send(confirmCommand);
                console.log('User auto-confirmed for development environment');
            } catch (confirmError) {
                console.error('Failed to auto-confirm user:', confirmError);
                // Continue anyway - the user might already be confirmed
            }
        }

        // The user is created but not confirmed yet - they need to verify their email (unless in development)
        // We'll create a temporary user record that will be updated after email verification
        const tempUserId = signUpResult.UserSub;
        if (!tempUserId) {
            return createResponse(500, { error: 'Failed to get user ID from Cognito' });
        }

        // Generate a random username for the user
        let generatedUsername: string | undefined;
        try {
            if (!USERS_TABLE) {
                throw new Error('Users table configuration missing');
            }
            const usernameResult = await generateAvailableUsername(USERS_TABLE);
            if (usernameResult) {
                generatedUsername = usernameResult;
                console.log(`Generated username for user ${tempUserId}: ${generatedUsername}`);
            } else {
                console.warn(`Failed to generate username for user ${tempUserId}, will be undefined`);
            }
        } catch (error) {
            console.error('Error generating username:', error);
            // Continue without username - user can set one later
        }

        // Create user record in DynamoDB (without email or cognitoUserId - those go in UserAuths)
        const userRecord: UserRecord = {
            id: tempUserId,
            username: generatedUsername, // Include generated username if available
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        if (!USERS_TABLE) {
            return createResponse(500, { error: 'Users table configuration missing' });
        }

        const putCommand = new PutCommand({
            TableName: USERS_TABLE,
            Item: userRecord
        });
        await dynamodb.send(putCommand);

        // Create UserAuth record for email authentication with CognitoID as providerUserId
        await userAuthService.createUserAuth({
            userId: tempUserId,
            provider: AuthProvider.EMAIL,
            providerUserId: normalizedEmail, // Store Cognito username (email) as providerUserId
            email: normalizedEmail,
            isPrimary: true,
            isVerified: !requiresVerification // If verification is disabled, mark as verified
        });

        // Log user account creation
        try {
            const { ipAddress, userAgent } = extractRequestMetadata(event);
            await logAccountCreated(tempUserId, normalizedEmail, ipAddress, userAgent);
        } catch (historyError) {
            console.error('Failed to log account creation:', historyError);
            // Don't fail the signup if history logging fails
        }

        // Return different messages based on whether verification is required
        const message = requiresVerification
            ? 'User created successfully. Please check your email to verify your account before signing in.'
            : 'User created successfully';

        return createResponse(201, {
            message,
            requiresVerification,
            user: {
                id: tempUserId,
                email: normalizedEmail,
                username: generatedUsername
            }
        });

    } catch (error: any) {
        console.error('SignUp error:', error);

        // Handle specific Cognito errors
        if (error.name === 'UsernameExistsException') {
            return createResponse(409, {
                error: 'Email already registered',
                details: 'An account with this email already exists. Try logging in instead.'
            });
        } else if (error.name === 'InvalidPasswordException') {
            return createResponse(400, {
                error: 'Invalid password',
                details: 'Password must be at least 8 characters long and contain uppercase, lowercase, and numeric characters.'
            });
        } else if (error.name === 'InvalidParameterException') {
            if (error.message?.toLowerCase().includes('password')) {
                return createResponse(400, {
                    error: 'Invalid password',
                    details: 'Password must be at least 8 characters long and contain uppercase, lowercase, and numeric characters.'
                });
            } else if (error.message?.toLowerCase().includes('email')) {
                return createResponse(400, {
                    error: 'Invalid email',
                    details: 'Please enter a valid email address.'
                });
            } else {
                return createResponse(400, {
                    error: 'Invalid request',
                    details: 'Please check your input and try again.'
                });
            }
        } else if (error.name === 'TooManyRequestsException') {
            return createResponse(429, {
                error: 'Too many attempts',
                details: 'Too many sign-up attempts. Please try again later.'
            });
        } else {
            return createResponse(500, {
                error: 'Failed to create user',
                details: 'An unexpected error occurred. Please try again.'
            });
        }
    }
};
