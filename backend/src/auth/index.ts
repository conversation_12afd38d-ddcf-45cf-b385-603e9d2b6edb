import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { handler as signinHandler } from './signin';
import { handler as refreshHandler } from './refresh';
import { handler as validateHandler } from './validate';
import { handler as appleSigninHandler } from './apple-signin';

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Main handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path } = event;

    // Handle CORS preflight requests
    if (httpMethod === 'OPTIONS') {
        return createResponse(200, {});
    }

    try {
        console.log(`Processing: ${httpMethod} ${path}`);

        // Route based on HTTP method and path (REST approach)
        if (httpMethod === 'POST' && path === '/auth/signin') {
            console.log('Calling signinHandler');
            return await signinHandler(event);
        } else if (httpMethod === 'POST' && path === '/auth/refresh') {
            console.log('Calling refreshHandler');
            return await refreshHandler(event);
        } else if (httpMethod === 'GET' && path === '/auth/validate') {
            console.log('Calling validateHandler');
            return await validateHandler(event);
        } else if (httpMethod === 'POST' && path === '/auth/apple') {
            console.log('Calling appleSigninHandler');
            return await appleSigninHandler(event);
        } else {
            console.log(`No matching route for: ${httpMethod} ${path}`);
            return createResponse(404, { error: 'Not found', message: `Unsupported route: ${httpMethod} ${path}` });
        }
    } catch (error: any) {
        console.error('Handler error:', error);
        return createResponse(500, { error: 'Internal server error', details: error.message });
    }
};
