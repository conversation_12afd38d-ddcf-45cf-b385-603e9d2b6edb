// Removed Cognito imports - <PERSON> auth bypasses Cognito entirely
import {
    DynamoDBClient
} from '@aws-sdk/client-dynamodb';
import {
    DynamoDBDocumentClient,
    PutCommand,
    GetCommand,
    UpdateCommand
} from '@aws-sdk/lib-dynamodb';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { v4 as uuidv4 } from 'uuid';
import * as jwt from 'jsonwebtoken';
import * as jose from 'node-jose';
import { getAppleConfig } from './apple-config';
import { logAppleSignIn, logAppleSignUp, logAccountLinkedApple, extractRequestMetadata } from '/opt/nodejs/user-history-service';
import { UserAuthService } from '/opt/nodejs/user-auth-service';
import { AuthProvider } from '/opt/nodejs/user-auth';
import { generateAuthTokensWithContext } from '/opt/nodejs/jwt-auth-service';
import { createResponse, generateAvailableUsername } from '/opt/nodejs/lambda-helpers';
import { formatDisplayName } from '/opt/nodejs/display-name-config';

// Types
interface AppleSignInRequest {
    identityToken: string;
    authorizationCode: string;
    user?: {
        name?: {
            firstName?: string;
            lastName?: string;
        };
        email?: string;
    };
}

interface AppleTokenPayload {
    iss: string;
    aud: string;
    exp: number;
    iat: number;
    sub: string;
    email?: string;
    email_verified?: boolean;
}

interface UserRecord {
    id: string;
    email?: string; // Now optional - stored in UserAuths table
    username?: string;
    firstName?: string;
    lastName?: string;
    displayName?: string; // Always includes provider prefix like "[apple]Username123"
    appleUserId?: string; // This will be removed - stored in UserAuths
    createdAt: string;
    updatedAt: string;
}

// Configure AWS SDK
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

const dynamoClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamoClient);

// Environment variables
const USERS_TABLE = process.env.USERS_TABLE || process.env.USERSTABLE_TABLE_NAME;
const USER_AUTHS_TABLE = process.env.USER_AUTHS_TABLE || process.env.USERAUTHS_TABLE_NAME;

// Initialize UserAuth service
const userAuthService = new UserAuthService(USER_AUTHS_TABLE);

// Verify Apple identity token
const verifyAppleToken = async (identityToken: string): Promise<AppleTokenPayload> => {
    try {
        // Get Apple configuration from Secrets Manager
        const appleConfig = await getAppleConfig();

        // Fetch Apple's public keys
        const response = await fetch('https://appleid.apple.com/auth/keys');
        const keys = await response.json() as { keys: any[] };

        // Decode the token header to get the key ID
        const tokenHeader = jwt.decode(identityToken, { complete: true });
        if (!tokenHeader || typeof tokenHeader === 'string') {
            throw new Error('Invalid token format');
        }

        const kid = tokenHeader.header.kid;
        const key = keys.keys.find((k: any) => k.kid === kid);

        if (!key) {
            throw new Error('Apple public key not found');
        }

        // Convert JWK to PEM format using node-jose
        const keyStore = jose.JWK.createKeyStore();
        const jwkKey = await keyStore.add(key);
        const publicKey = jwkKey.toPEM();

        // Verify and decode the token
        const payload = jwt.verify(identityToken, publicKey, {
            algorithms: ['RS256'],
            audience: appleConfig.clientId,
            issuer: 'https://appleid.apple.com'
        }) as AppleTokenPayload;

        return payload;
    } catch (error) {
        console.error('Apple token verification failed:', error);
        throw new Error('Invalid Apple identity token');
    }
};



// Find existing user by Apple ID using UserAuths
const findUserByAppleId = async (appleUserId: string): Promise<UserRecord | null> => {
    try {
        const userAuth = await userAuthService.findUserAuthByProvider({
            provider: AuthProvider.APPLE,
            providerUserId: appleUserId
        });

        if (!userAuth) {
            return null;
        }

        // Get the user record from Users table
        const getUserCommand = new GetCommand({
            TableName: USERS_TABLE,
            Key: { id: userAuth.userId }
        });
        const userResult = await dynamodb.send(getUserCommand);
        return userResult.Item as UserRecord || null;
    } catch (error) {
        console.error('Error finding user by Apple ID:', error);
        return null;
    }
};

// Find existing user by email using UserAuths (any provider)
const findUserByEmail = async (email: string): Promise<UserRecord | null> => {
    try {
        // Look for any auth record with this email (could be email provider or Apple with email)
        const userAuth = await userAuthService.findUserAuthByProvider({
            provider: AuthProvider.EMAIL,
            email: email.toLowerCase()
        });

        if (!userAuth) {
            return null;
        }

        // Get the user record from Users table
        const getUserCommand = new GetCommand({
            TableName: USERS_TABLE,
            Key: { id: userAuth.userId }
        });
        const userResult = await dynamodb.send(getUserCommand);
        return userResult.Item as UserRecord || null;
    } catch (error) {
        console.error('Error finding user by email:', error);
        return null;
    }
};

// Removed createOrUpdateCognitoUser - Apple auth bypasses Cognito

// Create user record in DynamoDB
const createUserRecord = async (
    email: string,
    appleUserId: string,
    firstName?: string,
    lastName?: string
): Promise<UserRecord> => {
    const userId = uuidv4();
    const now = new Date().toISOString();

    // Generate a username for the user
    const generatedUsername = await generateAvailableUsername('gamer');

    const userRecord: UserRecord = {
        id: userId,
        username: generatedUsername || undefined,
        firstName: firstName || '',
        lastName: lastName || '',
        displayName: generatedUsername ? formatDisplayName(generatedUsername, 'apple') : undefined, // Format as "[apple]Username123"
        appleUserId,
        createdAt: now,
        updatedAt: now
    };

    const params = {
        TableName: USERS_TABLE,
        Item: userRecord
    };

    await dynamodb.send(new PutCommand(params));

    // Create Apple authentication record in UserAuths table
    await userAuthService.createUserAuth({
        userId: userId,
        provider: AuthProvider.APPLE,
        providerUserId: appleUserId,
        email: email.toLowerCase(),
        providerData: {
            firstName: firstName,
            lastName: lastName
        },
        isPrimary: true,
        isVerified: true
    });

    return userRecord;
};

// Removed authenticateWithCognito - Apple auth uses JWT tokens instead

// Main Apple Sign In handler
export const appleSignIn = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { identityToken, authorizationCode, user }: AppleSignInRequest = JSON.parse(event.body);

        if (!identityToken) {
            return createResponse(400, { error: 'Identity token is required' });
        }

        // Verify required environment variables
        if (!USERS_TABLE || !USER_AUTHS_TABLE) {
            return createResponse(500, { error: 'Server configuration missing' });
        }

        // Get Apple configuration from Secrets Manager
        let appleConfig;
        try {
            appleConfig = await getAppleConfig();
        } catch (error) {
            console.error('Failed to get Apple configuration:', error);
            return createResponse(500, { error: 'Apple configuration missing' });
        }

        // Verify Apple identity token
        const applePayload = await verifyAppleToken(identityToken);
        const appleUserId = applePayload.sub;
        const email = applePayload.email || user?.email;

        if (!email) {
            return createResponse(400, { error: 'Email is required for Apple Sign In' });
        }

        // Check if user already exists by Apple ID
        let existingUser = await findUserByAppleId(appleUserId);
        let isNewUser = false;
        let isAccountLinking = false;

        if (!existingUser) {
            // Check if user exists by email
            existingUser = await findUserByEmail(email);

            if (existingUser) {
                // Link Apple ID to existing account
                isAccountLinking = true;
                existingUser.appleUserId = appleUserId;
                existingUser.updatedAt = new Date().toISOString();

                const updateParams = {
                    TableName: USERS_TABLE,
                    Key: { id: existingUser.id },
                    UpdateExpression: 'SET appleUserId = :appleUserId, updatedAt = :updatedAt',
                    ExpressionAttributeValues: {
                        ':appleUserId': appleUserId,
                        ':updatedAt': existingUser.updatedAt
                    }
                };

                await dynamodb.send(new UpdateCommand(updateParams));

                // Create Apple authentication record in UserAuths table
                await userAuthService.createUserAuth({
                    userId: existingUser.id,
                    provider: AuthProvider.APPLE,
                    providerUserId: appleUserId,
                    email: email.toLowerCase(),
                    providerData: {
                        firstName: user?.name?.firstName,
                        lastName: user?.name?.lastName
                    },
                    isPrimary: false, // Apple is secondary to existing email auth
                    isVerified: true
                });
            } else {
                // Create new user
                isNewUser = true;
                existingUser = await createUserRecord(
                    email,
                    appleUserId,
                    user?.name?.firstName,
                    user?.name?.lastName
                );
            }
        }

        // Log the appropriate action
        try {
            const { ipAddress, userAgent } = extractRequestMetadata(event);

            if (isNewUser) {
                await logAppleSignUp(existingUser.id, email, ipAddress, userAgent);
            } else if (isAccountLinking) {
                await logAccountLinkedApple(existingUser.id, email, ipAddress, userAgent);
            } else {
                await logAppleSignIn(existingUser.id, email, ipAddress, userAgent);
            }
        } catch (historyError) {
            console.error('Failed to log Apple authentication:', historyError);
            // Don't fail the request if history logging fails
        }

        // Generate JWT tokens with user context
        const jwtTokens = await generateAuthTokensWithContext(existingUser.id);

        // Check if username is required
        const requiresUsername = !existingUser.username || existingUser.username.trim() === '';

        return createResponse(200, {
            message: 'Apple Sign In successful',
            user: {
                id: existingUser.id,
                username: existingUser.username,
                firstName: existingUser.firstName,
                lastName: existingUser.lastName
            },
            tokens: {
                accessToken: jwtTokens.accessToken,
                refreshToken: jwtTokens.refreshToken,
                idToken: jwtTokens.idToken
            },
            requiresUsername
        });

    } catch (error: any) {
        console.error('Apple Sign In error:', error);
        return createResponse(500, {
            error: 'Apple Sign In failed',
            message: error.message || 'An unexpected error occurred'
        });
    }
};

// Lambda handler export
export const handler = appleSignIn;

export { verifyAppleToken, findUserByAppleId, findUserByEmail };
