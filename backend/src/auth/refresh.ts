import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { refreshAccessToken } from '/opt/nodejs/jwt-auth-service';
import { createResponse } from '/opt/nodejs/lambda-helpers';

// Types
interface RefreshTokenRequest {
    refreshToken: string;
}

// Refresh token handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('RefreshToken Event:', JSON.stringify(event, null, 2));

    // Handle CORS preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return createResponse(200, {});
    }

    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { refreshToken }: RefreshTokenRequest = JSON.parse(event.body);

        if (!refreshToken) {
            return createResponse(400, { error: 'Refresh token is required' });
        }

        // Refresh the access token using JWT
        const newTokens = await refreshAccessToken(refreshToken);

        return createResponse(200, {
            message: 'Token refreshed successfully',
            tokens: {
                accessToken: newTokens.accessToken,
                refreshToken: newTokens.refreshToken,
                idToken: newTokens.idToken,
                expiresIn: newTokens.expiresIn,
                tokenType: newTokens.tokenType
            }
        });

    } catch (error: any) {
        console.error('RefreshToken error:', error);

        if (error.message === 'Invalid refresh token') {
            return createResponse(401, { error: 'Invalid refresh token' });
        }

        return createResponse(500, { error: 'Internal server error' });
    }
};
