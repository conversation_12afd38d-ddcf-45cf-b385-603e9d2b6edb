{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "../", "typeRoots": ["./node_modules/@types"], "skipLibCheck": true, "strict": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "baseUrl": ".", "paths": {"/opt/nodejs/*": ["../layers/utils-layer/nodejs/*"]}}, "include": ["*.ts", "**/*.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}