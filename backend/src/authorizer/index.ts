import jwt from 'jsonwebtoken';
import { APIGatewayRequestAuthorizerEvent } from 'aws-lambda';
import { getSecret } from '@aws-lambda-powertools/parameters/secrets';

// Environment variables
const JWT_SECRET_NAME = process.env.JWT_SECRET_NAME;

// Helper function to get JWT secret from AWS Secrets Manager with intelligent caching
const getJWTSecret = async (): Promise<string> => {
    if (!JWT_SECRET_NAME) {
        throw new Error('JWT_SECRET_NAME environment variable not set');
    }

    try {
        // AWS Lambda Powertools provides intelligent caching that survives across invocations
        // Cache TTL is 5 minutes by default, and it handles cache invalidation automatically
        const secretString = await getSecret(JWT_SECRET_NAME, {
            maxAge: 300, // Cache for 5 minutes
            transform: 'json' // Automatically parse JSON
        });

        if (!secretString || typeof secretString !== 'object') {
            throw new Error('JWT secret not found or invalid format');
        }

        const secretData = secretString as { secret_key?: string };
        const jwtSecret = secretData.secret_key;

        if (!jwtSecret) {
            throw new Error('JWT secret_key not found in secret');
        }

        return jwtSecret;
    } catch (error) {
        console.error('Failed to get JWT secret:', error);
        throw new Error('Failed to retrieve JWT secret');
    }
};

// TypeScript interfaces
interface PolicyDocument {
    Version: string;
    Statement: PolicyStatement[];
}

interface PolicyStatement {
    Action: string;
    Effect: 'Allow' | 'Deny';
    Resource: string;
}

interface AuthorizerResponse {
    principalId: string;
    policyDocument: PolicyDocument;
    context?: Record<string, string>;
}

interface JWTPayload {
    sub: string; // User ID
    iat: number; // Issued at
    exp: number; // Expires at
    token_use: 'access' | 'refresh' | 'id';
    email?: string;
    username?: string;
    displayName?: string;
    authProviders?: string[]; // List of auth providers (email, xbox, twitch, apple)
    linkedUsernames?: string[]; // List of usernames from linked accounts
}



// Helper function to generate policy
const generatePolicy = (principalId: string, effect: 'Allow' | 'Deny', resource: string, context?: Record<string, string>): AuthorizerResponse => {
    return {
        principalId,
        policyDocument: {
            Version: '2012-10-17',
            Statement: [
                {
                    Action: 'execute-api:Invoke',
                    Effect: effect,
                    Resource: resource
                }
            ]
        },
        context
    };
};


// Main authorizer function
export const handler = async (event: APIGatewayRequestAuthorizerEvent): Promise<AuthorizerResponse> => {
    console.log('Authorizer event:', JSON.stringify(event, null, 2));

    try {
        // Extract token from Authorization header
        const authHeader = event.headers?.Authorization || event.headers?.authorization;

        if (!authHeader) {
            console.log('No Authorization header found');
            return generatePolicy('user', 'Deny', event.methodArn);
        }

        // Remove 'Bearer ' prefix
        const token = authHeader.replace(/^Bearer\s+/i, '');

        if (!token) {
            console.log('No token found after removing Bearer prefix');
            return generatePolicy('user', 'Deny', event.methodArn);
        }

        // Verify JWT token
        let decoded: JWTPayload;
        try {
            const jwtSecret = await getJWTSecret();
            decoded = jwt.verify(token, jwtSecret) as JWTPayload;
            console.log('JWT decoded successfully:', { sub: decoded.sub, token_use: decoded.token_use });
        } catch (jwtError: any) {
            console.error('JWT verification failed:', jwtError.message);
            return generatePolicy('user', 'Deny', event.methodArn);
        }

        // Validate token type (should be access token)
        if (decoded.token_use !== 'access') {
            console.log('Invalid token type:', decoded.token_use);
            return generatePolicy('user', 'Deny', event.methodArn);
        }

        // Create context for the Lambda function from JWT payload
        const context: Record<string, string> = {
            userId: decoded.sub,
            email: decoded.email || '',
            username: decoded.username || '',
            displayName: decoded.displayName || '',
            authProviders: (decoded.authProviders || []).join(','),
            linkedUsernames: (decoded.linkedUsernames || []).join(','),
            authType: 'jwt'
        };

        console.log('Authorization successful, context:', context);

        // Generate allow policy for all resources in this API
        const apiArn = event.methodArn.split('/').slice(0, 2).join('/') + '/*';
        return generatePolicy(decoded.sub, 'Allow', apiArn, context);

    } catch (error: any) {
        console.error('Authorizer error:', error);
        return generatePolicy('user', 'Deny', event.methodArn);
    }
};
