{"name": "gameflex-authorizer", "version": "1.0.0", "description": "GameFlex Lambda Authorizer Function", "main": "index.ts", "scripts": {"build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@aws-sdk/client-secrets-manager": "^3.0.0", "@aws-lambda-powertools/parameters": "^2.0.0", "@types/jsonwebtoken": "^9.0.10", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"@types/aws-lambda": "^8.10.0", "@types/node": "^20.0.0", "typescript": "^5.0.0"}, "keywords": ["aws", "lambda", "authorizer", "cognito", "typescript"], "author": "GameFlex", "license": "MIT"}