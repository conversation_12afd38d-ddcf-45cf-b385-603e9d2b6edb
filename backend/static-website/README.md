# GameFlex Landing Page

A modern, responsive landing page for the GameFlex social gaming platform, optimized for Cloudflare Pages deployment.

## Features

- 🎮 Gaming-focused design with platform integrations
- 📱 Fully responsive mobile-first design
- ⚡ Optimized for Cloudflare Pages with custom headers and redirects
- 🔍 SEO optimized with structured data and meta tags
- 🚀 Performance optimized with proper caching strategies
- 🎨 Modern CSS with GameFlex brand colors and gradients

## Cloudflare Pages Deployment

This landing page is pre-configured for Cloudflare Pages deployment with:

### Configuration Files

- `_headers` - Security and performance headers
- `_redirects` - URL redirects and routing rules
- `robots.txt` - Search engine crawler instructions
- `sitemap.xml` - Site structure for search engines

### Deployment Steps

1. **Connect Repository**: Import this repository to Cloudflare Pages
2. **Build Settings**:
   - Framework preset: `None` (static site)
   - Build command: (leave empty)
   - Build output directory: `/` (root directory)
3. **Custom Domain**: Configure your custom domain in Cloudflare Pages settings
4. **Environment Variables**: None required for basic deployment

### Performance Features

- **Caching Strategy**:
  - Static assets (CSS, JS, images): 1 year cache
  - HTML files: 1 hour cache for quick updates
  - Proper cache headers for optimal performance

- **Security Headers**:
  - X-Frame-Options, X-Content-Type-Options
  - XSS Protection and Referrer Policy
  - Content Security Policy ready

### SEO Optimization

- Complete meta tags for social sharing (Open Graph, Twitter Cards)
- Structured data for search engines
- Canonical URLs and proper robots.txt
- Sitemap.xml for search engine indexing

## File Structure

```
gameflex-landing-page/
├── index.html          # Main landing page
├── styles.css          # Stylesheet with GameFlex branding
├── _headers            # Cloudflare Pages headers config
├── _redirects          # Cloudflare Pages redirects config
├── robots.txt          # Search engine instructions
├── sitemap.xml         # Site structure
├── assets/             # Static assets
│   ├── icon_teal.svg   # GameFlex icon
│   └── gameflex_text.svg # GameFlex text logo
└── README.md           # This file
```

## Customization

### Update Domain References

Before deploying, update the domain references in:

1. `index.html` - Update meta tags and canonical URLs
2. `sitemap.xml` - Update the site URL
3. `robots.txt` - Update sitemap URL

### App Store Links

When your mobile apps are ready, update the download button links in `index.html`:

```html
<!-- Update these href attributes -->
<a href="YOUR_IOS_APP_STORE_LINK" class="download-btn ios-btn">
<a href="YOUR_GOOGLE_PLAY_STORE_LINK" class="download-btn android-btn">
```

### Social Media Links

Uncomment and update the social media redirects in `_redirects`:

```
/twitter https://twitter.com/yourusername 302
/discord https://discord.gg/yourserver 302
```

## Development

For local development, simply open `index.html` in a web browser or use a local server:

```bash
# Using Python
python -m http.server 8000

# Using Node.js
npx serve .

# Using PHP
php -S localhost:8000
```

## License

© 2024 GameFlex. All rights reserved.
