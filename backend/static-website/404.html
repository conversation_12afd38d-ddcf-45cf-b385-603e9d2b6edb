<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Primary Meta Tags -->
    <title>Page Not Found - GameFlex</title>
    <meta name="title" content="Page Not Found - GameFlex">
    <meta name="description" content="The page you're looking for doesn't exist. Return to GameFlex homepage.">
    <meta name="robots" content="noindex, nofollow">

    <!-- Favicon and Icons -->
    <link rel="icon" type="image/svg+xml" href="assets/icon_teal.svg">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link rel="apple-touch-icon" href="assets/apple-touch-icon.png">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <style>
        .error-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            text-align: center;
            padding: 2rem;
        }

        .error-code {
            font-size: 8rem;
            font-weight: 700;
            color: #28F4C3;
            margin: 0;
            line-height: 1;
        }

        .error-title {
            font-size: 2rem;
            font-weight: 500;
            color: #FFFFFF;
            margin: 1rem 0;
        }

        .error-description {
            font-size: 1.1rem;
            color: #B0BEC5;
            margin-bottom: 2rem;
            max-width: 500px;
        }

        .home-button {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: linear-gradient(135deg, #28F4C3 0%, #20C997 100%);
            color: #2A3642;
            text-decoration: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 500;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 244, 195, 0.3);
        }

        .home-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 244, 195, 0.4);
        }

        .logo-container {
            margin-bottom: 2rem;
        }

        .app-icon {
            width: 80px;
            height: 80px;
        }
    </style>
</head>

<body>
    <div class="error-container">
        <div class="logo-container">
            <img src="assets/icon_teal.svg" alt="GameFlex Icon" class="app-icon">
        </div>
        
        <h1 class="error-code">404</h1>
        <h2 class="error-title">Page Not Found</h2>
        <p class="error-description">
            Oops! The page you're looking for doesn't exist. 
            It might have been moved, deleted, or you entered the wrong URL.
        </p>
        
        <a href="/" class="home-button">
            <span>← Back to Home</span>
        </a>
    </div>

    <script>
        // Auto-redirect to home after 10 seconds
        setTimeout(() => {
            window.location.href = '/';
        }, 10000);
    </script>
</body>

</html>
