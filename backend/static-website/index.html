<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Primary Meta Tags -->
    <title>GameFlex - Social Gaming Platform</title>
    <meta name="title" content="GameFlex - Social Gaming Platform">
    <meta name="description"
        content="GameFlex - Connect with gamers, share content, and discover gaming communities. Available on iOS and Android.">
    <meta name="keywords" content="gaming, social, platform, xbox, playstation, steam, mobile app, ios, android">
    <meta name="author" content="GameFlex">
    <meta name="robots" content="index, follow">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://gameflex.io/">
    <meta property="og:title" content="GameFlex - Social Gaming Platform">
    <meta property="og:description"
        content="Connect with gamers, share content, and discover gaming communities. Available on iOS and Android.">
    <meta property="og:image" content="https://gameflex.io/assets/icon_teal.svg">
    <meta property="og:site_name" content="GameFlex">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://gameflex.io/">
    <meta property="twitter:title" content="GameFlex - Social Gaming Platform">
    <meta property="twitter:description"
        content="Connect with gamers, share content, and discover gaming communities. Available on iOS and Android.">
    <meta property="twitter:image" content="https://gameflex.io/assets/icon_teal.svg">

    <!-- Favicon and Icons -->
    <link rel="icon" type="image/svg+xml" href="assets/icon_teal.svg">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link rel="apple-touch-icon" href="assets/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="192x192" href="assets/icon-192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="assets/icon-512.png">

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#28F4C3">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="GameFlex">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://gameflex.io/">

    <!-- Performance optimizations -->
    <link rel="preload" href="assets/icon_teal.svg" as="image" type="image/svg+xml">
    <link rel="preload" href="assets/gameflex_text.svg" as="image" type="image/svg+xml">
    <link rel="preload" href="styles.css" as="style">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        crossorigin="anonymous" referrerpolicy="no-referrer">

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "GameFlex",
        "description": "Social gaming platform for connecting with gamers and sharing content",
        "applicationCategory": "GameApplication",
        "operatingSystem": ["iOS", "Android"],
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.5",
            "ratingCount": "1000"
        }
    }
    </script>
</head>

<body>
    <div class="container">
        <!-- Header Section -->
        <header class="header">
            <div class="logo-container">
                <img src="assets/icon_teal.svg" alt="GameFlex Icon" class="app-icon">
                <img src="assets/gameflex_text.svg" alt="GameFlex" class="text-logo">
            </div>
        </header>

        <!-- Hero Section -->
        <main class="hero">
            <div class="hero-content">
                <h1 class="hero-title">Go FLEX yourself!</h1>
                <p class="hero-description">
                    Turn your best plays into instant FLEXs. Share your gaming moments,
                    connect with fellow gamers, and discover amazing gaming communities.
                </p>

                <!-- App Download Buttons -->
                <div class="download-buttons">
                    <a href="#" class="download-badge" aria-label="Download GameFlex on iOS App Store">
                        <img src="assets/Download_on_the_App_Store_Badge_US-UK_RGB_blk_092917.svg"
                            alt="Download on the App Store" class="app-store-badge">
                    </a>

                    <a href="#" class="download-badge" aria-label="Download GameFlex on Google Play Store">
                        <img src="assets/GetItOnGooglePlay_Badge_Web_color_English.png" alt="Get it on Google Play"
                            class="google-play-badge">
                    </a>
                </div>
            </div>
        </main>

        <!-- Features Section -->
        <section class="features">
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fab fa-xbox platform-icon xbox" title="Xbox"></i>
                        <i class="fab fa-playstation platform-icon playstation" title="PlayStation"></i>
                        <i class="fab fa-steam platform-icon steam" title="Steam"></i>
                    </div>
                    <h3>Gaming Integration</h3>
                    <p>Connect your gaming accounts for seamless content sharing.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>Social Communities</h3>
                    <p>Join gaming channels and connect with players who share your interests.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-camera"></i>
                    </div>
                    <h3>Share Moments</h3>
                    <p>Capture and share your best gaming moments with built-in editing tools.</p>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer-content">
                <div class="footer-links">
                    <a href="terms-of-service.html" class="footer-link">Terms of Service</a>
                    <span class="footer-separator">•</span>
                    <a href="privacy-policy.html" class="footer-link">Privacy Policy</a>
                </div>
                <p class="footer-copyright">&copy; 2025 GameFlex. All rights reserved.</p>
            </div>
        </footer>
    </div>
</body>

</html>