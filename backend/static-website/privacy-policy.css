/* Privacy Policy Specific Styles */

/* Logo link styling */
.logo-link {
    text-decoration: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

/* Breadcrumb Navigation */
.breadcrumb {
    text-align: center;
    margin-top: 20px;
    padding: 15px 0;
    border-top: 1px solid rgba(40, 244, 195, 0.2);
}

.breadcrumb-link {
    color: var(--gf-text-light);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.breadcrumb-link:hover {
    color: var(--gf-green);
}

.breadcrumb-separator {
    color: var(--gf-gray-text);
    margin: 0 10px;
    font-size: 0.9rem;
}

.breadcrumb-current {
    color: var(--gf-green);
    font-size: 0.9rem;
    font-weight: 500;
}

/* Privacy Content Container */
.privacy-content {
    flex: 1;
    max-width: 900px;
    margin: 0 auto;
    padding: 40px 20px;
    background: rgba(26, 37, 48, 0.3);
    border-radius: 20px;
    margin-top: 20px;
    margin-bottom: 40px;
}

/* Privacy Header */
.privacy-header {
    text-align: center;
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 2px solid rgba(40, 244, 195, 0.2);
}

.privacy-header h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 15px;
    background: linear-gradient(45deg, var(--gf-green), var(--gf-blue));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.last-updated {
    color: var(--gf-text-light);
    font-size: 1rem;
    font-style: italic;
    margin: 0;
}

/* Privacy Body */
.privacy-body {
    line-height: 1.8;
}

/* Privacy Sections */
.privacy-section {
    margin-bottom: 40px;
}

.privacy-section:last-child {
    margin-bottom: 0;
}

.privacy-section h2 {
    color: var(--gf-green);
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(40, 244, 195, 0.2);
}

.privacy-section h3 {
    color: var(--gf-blue);
    font-size: 1.3rem;
    font-weight: 500;
    margin-bottom: 10px;
    margin-top: 20px;
}

.privacy-section p {
    color: var(--gf-off-white);
    margin-bottom: 15px;
    text-align: justify;
}

.privacy-section p:last-child {
    margin-bottom: 0;
}

/* Intro text styling */
.intro-text {
    font-size: 1.1rem;
    color: var(--gf-text-light);
    line-height: 1.8;
    margin-bottom: 0;
}

/* Information items */
.info-item {
    margin-bottom: 25px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-item h3 {
    margin-top: 0;
    margin-bottom: 12px;
    color: var(--gf-blue);
    font-size: 1.2rem;
}

.info-item p {
    margin-bottom: 0;
    color: var(--gf-off-white);
}

/* Privacy Lists */
.privacy-list {
    margin: 20px 0;
    padding-left: 0;
    list-style: none;
}

.privacy-list li {
    color: var(--gf-off-white);
    margin-bottom: 12px;
    padding-left: 25px;
    position: relative;
    line-height: 1.7;
}

.privacy-list li::before {
    content: "•";
    color: var(--gf-green);
    font-size: 1.2rem;
    position: absolute;
    left: 0;
    top: 0;
}

/* Email links */
.email-link {
    color: var(--gf-blue);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.email-link:hover {
    color: var(--gf-green);
    text-decoration: underline;
}

/* Copyright notice */
.copyright-notice {
    font-size: 0.9rem;
    color: var(--gf-gray-text);
    font-style: italic;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid rgba(40, 244, 195, 0.1);
}

/* Responsive Design for Privacy Policy */
@media (max-width: 768px) {
    .privacy-content {
        padding: 30px 15px;
        margin-top: 15px;
        margin-bottom: 30px;
    }

    .privacy-header h1 {
        font-size: 2.2rem;
    }

    .privacy-section {
        margin-bottom: 30px;
    }

    .privacy-section h2 {
        font-size: 1.5rem;
    }

    .privacy-section h3 {
        font-size: 1.2rem;
    }

    .info-item {
        margin-bottom: 20px;
    }

    .breadcrumb {
        padding: 10px 0;
    }

    .breadcrumb-link,
    .breadcrumb-separator,
    .breadcrumb-current {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .privacy-content {
        padding: 20px 10px;
    }

    .privacy-header {
        margin-bottom: 30px;
        padding-bottom: 20px;
    }

    .privacy-header h1 {
        font-size: 1.8rem;
    }

    .last-updated {
        font-size: 0.9rem;
    }

    .privacy-section {
        margin-bottom: 25px;
    }

    .privacy-section h2 {
        font-size: 1.3rem;
        margin-bottom: 15px;
    }

    .privacy-section h3 {
        font-size: 1.1rem;
    }

    .privacy-section p {
        font-size: 0.95rem;
        text-align: left;
    }

    .intro-text {
        font-size: 1rem;
    }

    .info-item {
        margin-bottom: 15px;
    }

    .info-item h3 {
        font-size: 1.1rem;
    }

    .privacy-list li {
        padding-left: 20px;
        font-size: 0.95rem;
    }
}

/* Print styles */
@media print {
    .privacy-content {
        background: white;
        color: black;
        box-shadow: none;
        border: none;
    }

    .privacy-section {
        break-inside: avoid;
    }

    .privacy-header h1 {
        color: black;
        background: none;
        -webkit-text-fill-color: initial;
    }

    .privacy-section h2,
    .privacy-section h3,
    .info-item h3 {
        color: black;
    }

    .email-link {
        color: blue;
        text-decoration: underline;
    }

    .breadcrumb,
    .footer {
        display: none;
    }
}