# GameFlex Backend GitLab CI/CD Pipeline
# Backend-focused pipeline with SAST, Secrets Detection, and build validation
#
# SETUP: Run ./setup/bootstrap.sh to automatically create users and configure environments
# For detailed setup instructions, see setup/SETUP.md

# Global variables
variables:
  # Docker images
  NODE_IMAGE: "node:18-alpine"
  AWS_CLI_IMAGE: "amazon/aws-cli:latest"

  # Node.js configuration
  NODE_VERSION: "18"
  NPM_CONFIG_CACHE: "$CI_PROJECT_DIR/.npm"

  # AWS configuration
  AWS_DEFAULT_REGION: "us-west-2"

  # Project configuration
  PROJECT_NAME: "gameflex"

  # Security scanning
  SAST_EXCLUDED_PATHS: "node_modules,build,dist,coverage"
  SAST_DEFAULT_ANALYZERS: "eslint,nodejs-scan,semgrep"
  DS_DEFAULT_ANALYZERS: "gemnasium"

# Pipeline stages
stages:
  - prepare
  - security
  - test
  - build

# Global cache configuration
cache:
  key:
    files:
      - package-lock.json
  paths:
    - node_modules/
    - .npm/

# Include GitLab security templates
include:
  - template: SAST.gitlab-ci.yml
  - template: Secret-Detection.gitlab-ci.yml
  - template: Dependency-Scanning.gitlab-ci.yml

# ============================================================================
# PREPARATION STAGE
# ============================================================================

# Install backend dependencies
backend-deps:
  stage: prepare
  image: $NODE_IMAGE
  script:
    - npm ci --cache .npm --prefer-offline
  cache:
    key: backend-deps-$CI_COMMIT_REF_SLUG
    paths:
      - node_modules/
      - .npm/
  artifacts:
    paths:
      - node_modules/
    expire_in: 1 hour
  only:
    changes:
      - "**/*"
      - .gitlab-ci.yml

# ============================================================================
# SECURITY STAGE
# ============================================================================

# SAST is automatically included from the template - no custom job needed

# Security scanning jobs are automatically included from templates

# ============================================================================
# TESTING STAGE - LOCAL TESTING ONLY
# ============================================================================

# Note: Backend testing removed from pipeline as server won't reflect current changes
# Run tests locally using: npm run test or npm run test:coverage

# ============================================================================
# BUILD STAGE
# ============================================================================

# Build Lambda functions
lambda-build:
  stage: build
  image: $NODE_IMAGE
  dependencies:
    - backend-deps
  script:
    - echo "Building TypeScript Lambda functions..."
    - npm run build
  artifacts:
    paths:
      - dist/
    expire_in: 1 day
  rules:
    - if: $CI_COMMIT_BRANCH
      changes:
        - "**/*"
    - if: $CI_MERGE_REQUEST_IID
      changes:
        - "**/*"
