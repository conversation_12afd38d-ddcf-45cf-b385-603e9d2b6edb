# Compiled JavaScript files
*.js
!jest.config.js
*.d.ts

# Dependencies
node_modules/
package-lock.json

# CDK asset staging directory
.cdk.staging
cdk.out/

# Lambda dist directories
*.zip
src/*/dist/
dist/

# Terraform files
terraform/.terraform/
terraform/.terraform.lock.hcl
terraform/terraform.tfstate
terraform/terraform.tfstate.backup
terraform/terraform.tfvars
terraform/terraform.tfvars.json
terraform/.terraform.tfstate.lock.info
terraform/tfplan
terraform/tfplan.json
terraform/crash.log
terraform/crash.*.log
terraform/override.tf
terraform/override.tf.json
terraform/*_override.tf
terraform/*_override.tf.json
terraform/.terraformrc
terraform/terraform.rc

# Environment and secrets
.env
.env.local
.env.development
.env.staging
.env.production
.env.test
*.pem
*.key
*.crt

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# AWS SAM
.aws-sam/

# Local test files
test-results/
test-output/