# GameFlex Backend - Terraform Infrastructure

This directory contains the Terraform infrastructure code for the GameFlex backend, migrated from AWS CDK.

## 🏗️ Architecture

The infrastructure is organized into modular components:

- **Database Module** - DynamoDB tables for application data
- **Authentication Module** - Cognito User Pool and related resources
- **Storage Module** - S3 bucket with CloudFront CDN for media
- **Notifications Module** - SNS topics and notification tables
- **Landing Page Module** - Static website hosting
- **Lambda Module** - All Lambda functions for API endpoints
- **API Gateway Module** - REST API with custom authorizers

## 📁 Directory Structure

```
terraform/
├── main.tf                    # Main Terraform configuration
├── variables.tf               # Input variables
├── outputs.tf                 # Output values
├── environments/              # Environment-specific configurations
│   ├── development.tfvars
│   ├── staging.tfvars
│   └── production.tfvars
├── backend-configs/           # Terraform state backend configurations
│   ├── development.hcl
│   ├── staging.hcl
│   └── production.hcl
└── modules/                   # Terraform modules
    ├── database/
    ├── authentication/
    ├── storage/
    ├── notifications/
    ├── landing_page/
    ├── lambda/
    └── api_gateway/
```

## 🚀 Deployment

### Prerequisites

1. **Terraform** (>= 1.0)
   ```bash
   # Install via Homebrew (macOS)
   brew install terraform
   
   # Or download from https://www.terraform.io/downloads.html
   ```

2. **AWS CLI** configured with appropriate credentials
   ```bash
   aws configure
   # or set environment variables: AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY
   ```

3. **jq** for JSON parsing
   ```bash
   brew install jq
   ```

### State Management Setup

Before first deployment, create the S3 buckets and DynamoDB tables for Terraform state:

```bash
# Create state bucket for development
aws s3 mb s3://gameflex-terraform-state-development --region us-west-2

# Create lock table for development
aws dynamodb create-table \
    --table-name gameflex-terraform-locks-development \
    --attribute-definitions AttributeName=LockID,AttributeType=S \
    --key-schema AttributeName=LockID,KeyType=HASH \
    --provisioned-throughput ReadCapacityUnits=5,WriteCapacityUnits=5 \
    --region us-west-2

# Repeat for staging and production environments
```

### Deployment Commands

Use the new `terraform-deploy.sh` script (replaces the old `deploy.sh`):

```bash
# Deploy to development (default)
./terraform-deploy.sh

# Deploy to specific environment
./terraform-deploy.sh staging
./terraform-deploy.sh production

# Show plan before applying
./terraform-deploy.sh development --plan

# Plan only (don't apply)
./terraform-deploy.sh staging --plan-only

# Auto-approve (skip confirmation)
./terraform-deploy.sh development -y

# Destroy infrastructure
./terraform-deploy.sh development --destroy

# Verbose output
./terraform-deploy.sh development -v
```

### Environment Configuration

Each environment has its own configuration file in `environments/`:

- **development.tfvars** - Development environment settings
- **staging.tfvars** - Staging environment settings  
- **production.tfvars** - Production environment settings

Key differences between environments:

| Setting | Development | Staging | Production |
|---------|-------------|---------|------------|
| AWS Region | us-west-2 | us-west-2 | us-east-1 |
| Deletion Protection | false | true | true |
| Point-in-Time Recovery | false | true | true |
| Auto Email Verification | false | true | true |
| Lambda Memory | 256 MB | 512 MB | 1024 MB |
| Log Retention | 7 days | 30 days | 90 days |

## 🔧 Configuration

### Secrets Manager

The following secrets must be created in AWS Secrets Manager before deployment:

- `gameflex-app-config-{environment}` - Application configuration
- `gameflex-apple-config-{environment}` - Apple Sign-In configuration
- `gameflex-xbox-config-{environment}` - Xbox OAuth configuration

### Custom Domains

To use custom domains, update the certificate ARNs in the environment `.tfvars` files:

```hcl
# staging.tfvars
certificate_arn               = "arn:aws:acm:us-west-2:123456789012:certificate/..."
media_certificate_arn         = "arn:aws:acm:us-west-2:123456789012:certificate/..."
landing_page_certificate_arn  = "arn:aws:acm:us-west-2:123456789012:certificate/..."
```

## 📊 Outputs

After deployment, Terraform provides outputs for:

- API Gateway URL
- Cognito User Pool details
- DynamoDB table names and ARNs
- S3 bucket information
- CloudFront distribution details
- Lambda function ARNs

View outputs:
```bash
cd terraform
terraform output
```

## 🔄 Migration from CDK

This Terraform configuration replaces the previous AWS CDK setup. Key changes:

1. **Infrastructure as Code**: CDK TypeScript → Terraform HCL
2. **State Management**: CDK managed → Terraform S3 backend
3. **Deployment**: `deploy.sh` → `terraform-deploy.sh`
4. **Modularity**: CDK nested stacks → Terraform modules

### Migration Steps

1. ✅ Create Terraform modules for all CDK stacks
2. ✅ Set up environment-specific configurations
3. ✅ Create new deployment script
4. ⏳ Test deployment in development environment
5. ⏳ Migrate staging environment
6. ⏳ Migrate production environment

## 🛠️ Development

### Adding New Resources

1. Add resources to the appropriate module in `modules/`
2. Update module variables and outputs as needed
3. Test in development environment first
4. Update documentation

### Module Structure

Each module follows this structure:
```
module_name/
├── main.tf       # Resource definitions
├── variables.tf  # Input variables
└── outputs.tf    # Output values
```

## 🔍 Troubleshooting

### Common Issues

1. **State Lock**: If deployment fails with state lock error:
   ```bash
   terraform force-unlock <LOCK_ID>
   ```

2. **Resource Already Exists**: Use Terraform import:
   ```bash
   terraform import aws_s3_bucket.example bucket-name
   ```

3. **Permission Denied**: Ensure AWS credentials have sufficient permissions

### Debugging

Enable verbose logging:
```bash
export TF_LOG=DEBUG
./terraform-deploy.sh development -v
```

## 📚 Resources

- [Terraform Documentation](https://www.terraform.io/docs)
- [AWS Provider Documentation](https://registry.terraform.io/providers/hashicorp/aws/latest/docs)
- [Terraform Best Practices](https://www.terraform.io/docs/cloud/guides/recommended-practices/index.html)
