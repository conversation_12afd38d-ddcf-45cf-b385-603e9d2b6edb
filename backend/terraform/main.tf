# GameFlex Backend Infrastructure - Main Terraform Configuration
# This file defines the main infrastructure for the GameFlex backend

terraform {
  required_version = ">= 1.0"
  
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
  }

  # Backend configuration for state management
  # This will be configured per environment
  backend "s3" {}
}

# Configure the AWS Provider
provider "aws" {
  region = var.aws_region

  default_tags {
    tags = {
      Project     = var.project_name
      Environment = var.environment
      ManagedBy   = "Terraform"
    }
  }
}

# Data sources for existing resources
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# Local values for common configurations
locals {
  account_id = data.aws_caller_identity.current.account_id
  region     = data.aws_region.current.name
  
  # Common naming convention
  name_prefix = "${var.project_name}-${var.environment}"
  
  # Environment-specific settings
  is_production_or_staging = contains(["production", "staging"], var.environment)
  
  # Common tags
  common_tags = {
    Project     = var.project_name
    Environment = var.environment
    ManagedBy   = "Terraform"
  }
}

# Core Infrastructure Modules
module "database" {
  source = "./modules/database"
  
  project_name             = var.project_name
  environment              = var.environment
  is_production_or_staging = local.is_production_or_staging
  
  tags = local.common_tags
}

module "authentication" {
  source = "./modules/authentication"

  project_name             = var.project_name
  environment              = var.environment
  aws_region               = var.aws_region
  aws_account_id           = var.aws_account_id
  is_production_or_staging = local.is_production_or_staging
  enable_auto_verify_email = var.enable_auto_verify_email

  tags = local.common_tags
}

module "storage" {
  source = "./modules/storage"

  project_name             = var.project_name
  environment              = var.environment
  is_production_or_staging = local.is_production_or_staging
  media_domain_name        = var.media_domain_name
  media_certificate_arn    = var.media_certificate_arn

  # Dependencies
  media_table = module.database.tables.media
  users_table = module.database.tables.users

  tags = local.common_tags
}

module "notifications" {
  source = "./modules/notifications"
  
  project_name             = var.project_name
  environment              = var.environment
  is_production_or_staging = local.is_production_or_staging
  
  tags = local.common_tags
}

module "landing_page" {
  source = "./modules/landing_page"
  
  project_name             = var.project_name
  environment              = var.environment
  is_production_or_staging = local.is_production_or_staging
  certificate_arn          = var.landing_page_certificate_arn
  
  tags = local.common_tags
}

module "lambda_layer" {
  source = "./modules/lambda-layer"

  project_name = var.project_name
  environment  = var.environment
}

module "lambda_functions" {
  source = "./modules/lambda"

  project_name             = var.project_name
  environment              = var.environment
  is_production_or_staging = local.is_production_or_staging
  media_domain_name        = var.media_domain_name

  # Dependencies
  user_pool         = module.authentication.user_pool
  user_pool_client  = module.authentication.user_pool_client
  database_tables   = module.database.tables
  media_bucket      = module.storage.media_bucket
  distribution      = module.storage.distribution
  notification_stack = module.notifications
  utils_layer_arn   = module.lambda_layer.layer_arn

  # Secrets
  app_config_secret   = var.app_config_secret_name
  apple_config_secret = var.apple_config_secret_name
  xbox_config_secret  = var.xbox_config_secret_name
  twitch_config_secret = var.twitch_config_secret_name
  kick_config_secret  = var.kick_config_secret_name
  jwt_secret          = var.jwt_secret_name

  tags = local.common_tags
}

module "api_gateway" {
  source = "./modules/api_gateway"

  project_name             = var.project_name
  environment              = var.environment
  is_production_or_staging = local.is_production_or_staging
  domain_name              = var.domain_name
  certificate_arn          = var.certificate_arn

  # Dependencies
  authorizer_function = module.lambda_functions.authorizer_function
  lambda_functions    = module.lambda_functions.functions

  tags = local.common_tags
}

# JWT Secret for authentication
resource "aws_secretsmanager_secret" "jwt_secret" {
  name        = var.jwt_secret_name
  description = "JWT secret key for GameFlex ${var.environment} authentication"

  tags = local.common_tags
}

# Generate a random JWT secret key
resource "random_password" "jwt_secret_key" {
  length  = 64
  special = true
}

# Store the JWT secret key
resource "aws_secretsmanager_secret_version" "jwt_secret" {
  secret_id     = aws_secretsmanager_secret.jwt_secret.id
  secret_string = jsonencode({
    secret_key = random_password.jwt_secret_key.result
  })
}
