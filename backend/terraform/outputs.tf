# Outputs for GameFlex Backend Infrastructure

# API Gateway Outputs
output "api_gateway_url" {
  description = "URL of the API Gateway"
  value       = module.api_gateway.api_url
}

output "api_gateway_id" {
  description = "ID of the API Gateway"
  value       = module.api_gateway.api_id
}

output "api_gateway_stage" {
  description = "Stage name of the API Gateway"
  value       = module.api_gateway.stage_name
}

# Cognito Outputs
output "user_pool_id" {
  description = "ID of the Cognito User Pool"
  value       = module.authentication.user_pool_id
}

output "user_pool_client_id" {
  description = "ID of the Cognito User Pool Client"
  value       = module.authentication.user_pool_client_id
}

output "user_pool_domain" {
  description = "Domain of the Cognito User Pool"
  value       = module.authentication.user_pool_domain
}

# DynamoDB Outputs
output "dynamodb_table_names" {
  description = "Names of all DynamoDB tables"
  value       = module.database.table_names
}

output "dynamodb_table_arns" {
  description = "ARNs of all DynamoDB tables"
  value       = module.database.table_arns
}

# S3 and CloudFront Outputs
output "media_bucket_name" {
  description = "Name of the media S3 bucket"
  value       = module.storage.bucket_name
}

output "media_bucket_arn" {
  description = "ARN of the media S3 bucket"
  value       = module.storage.bucket_arn
}

output "media_cloudfront_distribution_id" {
  description = "ID of the media CloudFront distribution"
  value       = module.storage.distribution_id
}

output "media_cloudfront_domain_name" {
  description = "Domain name of the media CloudFront distribution"
  value       = module.storage.distribution_domain_name
}

output "media_cloudfront_url" {
  description = "URL of the media CloudFront distribution"
  value       = module.storage.distribution_url
}

# Landing Page Outputs
output "landing_page_bucket_name" {
  description = "Name of the landing page S3 bucket"
  value       = module.landing_page.bucket_name
}

output "landing_page_cloudfront_distribution_id" {
  description = "ID of the landing page CloudFront distribution"
  value       = module.landing_page.distribution_id
}

output "landing_page_cloudfront_domain_name" {
  description = "Domain name of the landing page CloudFront distribution"
  value       = module.landing_page.distribution_domain_name
}

output "landing_page_url" {
  description = "URL of the landing page"
  value       = module.landing_page.website_url
}

# Lambda Function Outputs
output "lambda_function_names" {
  description = "Names of all Lambda functions"
  value       = module.lambda_functions.function_names
}

output "lambda_function_arns" {
  description = "ARNs of all Lambda functions"
  value       = module.lambda_functions.function_arns
}

output "authorizer_function_arn" {
  description = "ARN of the API Gateway authorizer function"
  value       = module.lambda_functions.authorizer_function_arn
}

# SNS Outputs
output "notification_topic_arn" {
  description = "ARN of the notification SNS topic"
  value       = module.notifications.topic_arn
}

# Secrets Manager Outputs
output "app_config_secret_arn" {
  description = "ARN of the app configuration secret"
  value       = var.app_config_secret_name
}

output "apple_config_secret_arn" {
  description = "ARN of the Apple configuration secret"
  value       = var.apple_config_secret_name
}

output "xbox_config_secret_arn" {
  description = "ARN of the Xbox configuration secret"
  value       = var.xbox_config_secret_name
}

output "twitch_config_secret_arn" {
  description = "ARN of the Twitch configuration secret"
  value       = var.twitch_config_secret_name
}

output "kick_config_secret_arn" {
  description = "ARN of the Kick configuration secret"
  value       = var.kick_config_secret_name
}

# Environment Information
output "environment" {
  description = "Current environment"
  value       = var.environment
}

output "project_name" {
  description = "Project name"
  value       = var.project_name
}

output "aws_region" {
  description = "AWS region"
  value       = var.aws_region
}

output "aws_account_id" {
  description = "AWS account ID"
  value       = data.aws_caller_identity.current.account_id
}
