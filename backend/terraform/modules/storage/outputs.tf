# Outputs for Storage Module

# S3 Bucket outputs
output "media_bucket" {
  description = "S3 media bucket object"
  value       = aws_s3_bucket.media
}

output "bucket_name" {
  description = "Name of the media S3 bucket"
  value       = aws_s3_bucket.media.bucket
}

output "bucket_arn" {
  description = "ARN of the media S3 bucket"
  value       = aws_s3_bucket.media.arn
}

output "bucket_domain_name" {
  description = "Domain name of the media S3 bucket"
  value       = aws_s3_bucket.media.bucket_domain_name
}

output "bucket_regional_domain_name" {
  description = "Regional domain name of the media S3 bucket"
  value       = aws_s3_bucket.media.bucket_regional_domain_name
}

# CloudFront Distribution outputs
output "distribution" {
  description = "CloudFront distribution object"
  value       = aws_cloudfront_distribution.media
}

output "distribution_id" {
  description = "ID of the CloudFront distribution"
  value       = aws_cloudfront_distribution.media.id
}

output "distribution_arn" {
  description = "ARN of the CloudFront distribution"
  value       = aws_cloudfront_distribution.media.arn
}

output "distribution_domain_name" {
  description = "Domain name of the CloudFront distribution"
  value       = aws_cloudfront_distribution.media.domain_name
}

output "distribution_url" {
  description = "URL of the CloudFront distribution"
  value       = "https://${aws_cloudfront_distribution.media.domain_name}"
}

output "distribution_hosted_zone_id" {
  description = "Hosted zone ID of the CloudFront distribution"
  value       = aws_cloudfront_distribution.media.hosted_zone_id
}

# Route53 outputs (if created)
output "route53_record_names" {
  description = "Names of Route53 records created for media domain"
  value = var.media_domain_name != "" ? [
    aws_route53_record.media_a[0].name,
    aws_route53_record.media_aaaa[0].name
  ] : []
}

output "media_domain_url" {
  description = "URL of the media domain (if configured)"
  value       = var.media_domain_name != "" ? "https://${var.media_domain_name}" : ""
}
