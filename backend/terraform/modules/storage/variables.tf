# Variables for Storage Module

variable "project_name" {
  description = "Name of the project"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "is_production_or_staging" {
  description = "Whether this is a production or staging environment"
  type        = bool
}

variable "media_domain_name" {
  description = "Custom domain name for media CDN"
  type        = string
  default     = ""
}

variable "media_certificate_arn" {
  description = "ACM certificate ARN for media domain"
  type        = string
  default     = ""
}

variable "force_destroy" {
  description = "Force destroy S3 bucket even if it contains objects"
  type        = bool
  default     = false
}

variable "price_class" {
  description = "Price class for CloudFront distribution"
  type        = string
  default     = "PriceClass_100"
  validation {
    condition = contains([
      "PriceClass_All",
      "PriceClass_200",
      "PriceClass_100"
    ], var.price_class)
    error_message = "Price class must be one of: PriceClass_All, PriceClass_200, PriceClass_100."
  }
}

variable "media_table" {
  description = "DynamoDB media table object"
  type        = any
}

variable "users_table" {
  description = "DynamoDB users table object"
  type        = any
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default     = {}
}
