# Lambda Layer for shared utilities
data "archive_file" "utils_layer" {
  type        = "zip"
  source_dir  = "${path.root}/../src/layers/dist/utils-layer"
  output_path = "${path.root}/../src/layers/utils-layer.zip"
}

resource "aws_lambda_layer_version" "utils_layer" {
  filename         = data.archive_file.utils_layer.output_path
  layer_name       = "${var.project_name}-${var.environment}-utils"
  source_code_hash = data.archive_file.utils_layer.output_base64sha256

  compatible_runtimes = ["nodejs18.x", "nodejs20.x"]
  description         = "Shared utilities for GameFlex Lambda functions"

  lifecycle {
    create_before_destroy = true
  }
}
