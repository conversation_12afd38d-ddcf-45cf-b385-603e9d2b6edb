# Outputs for Notifications Module

# SNS Topic outputs
output "notification_topic" {
  description = "SNS notification topic object"
  value       = aws_sns_topic.notifications
}

output "topic_arn" {
  description = "ARN of the notification SNS topic"
  value       = aws_sns_topic.notifications.arn
}

output "topic_name" {
  description = "Name of the notification SNS topic"
  value       = aws_sns_topic.notifications.name
}

# DynamoDB Tables outputs
output "tables" {
  description = "Map of all notification-related DynamoDB tables"
  value = {
    notifications             = aws_dynamodb_table.notifications
    device_tokens            = aws_dynamodb_table.device_tokens
    notification_preferences = aws_dynamodb_table.notification_preferences
    notification_history     = aws_dynamodb_table.notification_history
  }
}

output "table_names" {
  description = "Map of notification table names"
  value = {
    notifications             = aws_dynamodb_table.notifications.name
    device_tokens            = aws_dynamodb_table.device_tokens.name
    notification_preferences = aws_dynamodb_table.notification_preferences.name
    notification_history     = aws_dynamodb_table.notification_history.name
  }
}

output "table_arns" {
  description = "Map of notification table ARNs"
  value = {
    notifications             = aws_dynamodb_table.notifications.arn
    device_tokens            = aws_dynamodb_table.device_tokens.arn
    notification_preferences = aws_dynamodb_table.notification_preferences.arn
    notification_history     = aws_dynamodb_table.notification_history.arn
  }
}

# IAM Role outputs
output "notification_lambda_role" {
  description = "IAM role for notification Lambda functions"
  value       = aws_iam_role.notification_lambda_role
}

output "notification_lambda_role_arn" {
  description = "ARN of the notification Lambda IAM role"
  value       = aws_iam_role.notification_lambda_role.arn
}
