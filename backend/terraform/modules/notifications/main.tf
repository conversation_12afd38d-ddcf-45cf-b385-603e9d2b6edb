# Notifications Module for GameFlex Backend - SNS and DynamoDB

locals {
  name_prefix = "${var.project_name}-${var.environment}"
  
  table_config = {
    billing_mode   = "PAY_PER_REQUEST"
    deletion_protection = var.is_production_or_staging
    point_in_time_recovery = var.is_production_or_staging
  }
}

# SNS Topic for Push Notifications
resource "aws_sns_topic" "notifications" {
  name = "${local.name_prefix}-notifications"

  # Enable encryption
  kms_master_key_id = "alias/aws/sns"

  tags = merge(var.tags, {
    Name = "${local.name_prefix}-notifications"
    Type = "Push Notifications"
  })
}

# SNS Topic Policy
resource "aws_sns_topic_policy" "notifications" {
  arn = aws_sns_topic.notifications.arn

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AllowLambdaPublish"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
        Action = [
          "sns:Publish"
        ]
        Resource = aws_sns_topic.notifications.arn
      }
    ]
  })
}

# DynamoDB Tables for Notifications

# Notifications Table - stores notification records
resource "aws_dynamodb_table" "notifications" {
  name           = "${local.name_prefix}-Notifications"
  billing_mode   = local.table_config.billing_mode
  hash_key       = "id"
  
  attribute {
    name = "id"
    type = "S"
  }
  
  attribute {
    name = "userId"
    type = "S"
  }
  
  attribute {
    name = "createdAt"
    type = "S"
  }
  
  attribute {
    name = "type"
    type = "S"
  }
  
  # GSI for user notifications
  global_secondary_index {
    name               = "userId-createdAt-index"
    hash_key           = "userId"
    range_key          = "createdAt"
    projection_type    = "ALL"
  }

  # GSI for notification type queries
  global_secondary_index {
    name               = "type-createdAt-index"
    hash_key           = "type"
    range_key          = "createdAt"
    projection_type    = "ALL"
  }
  
  deletion_protection_enabled = local.table_config.deletion_protection
  
  point_in_time_recovery {
    enabled = local.table_config.point_in_time_recovery
  }
  
  tags = merge(var.tags, {
    Name = "${local.name_prefix}-Notifications"
    Type = "Notifications Data"
  })
}

# Device Tokens Table - stores FCM/APNS tokens
resource "aws_dynamodb_table" "device_tokens" {
  name           = "${local.name_prefix}-DeviceTokens"
  billing_mode   = local.table_config.billing_mode
  hash_key       = "userId"
  range_key      = "deviceId"
  
  attribute {
    name = "userId"
    type = "S"
  }
  
  attribute {
    name = "deviceId"
    type = "S"
  }
  
  attribute {
    name = "token"
    type = "S"
  }
  
  # GSI for token lookup
  global_secondary_index {
    name               = "token-index"
    hash_key           = "token"
    projection_type    = "ALL"
  }
  
  deletion_protection_enabled = local.table_config.deletion_protection
  
  point_in_time_recovery {
    enabled = local.table_config.point_in_time_recovery
  }
  
  tags = merge(var.tags, {
    Name = "${local.name_prefix}-DeviceTokens"
    Type = "Device Management"
  })
}

# Notification Preferences Table - user notification settings
resource "aws_dynamodb_table" "notification_preferences" {
  name           = "${local.name_prefix}-NotificationPreferences"
  billing_mode   = local.table_config.billing_mode
  hash_key       = "userId"
  
  attribute {
    name = "userId"
    type = "S"
  }
  
  deletion_protection_enabled = local.table_config.deletion_protection
  
  point_in_time_recovery {
    enabled = local.table_config.point_in_time_recovery
  }
  
  tags = merge(var.tags, {
    Name = "${local.name_prefix}-NotificationPreferences"
    Type = "User Preferences"
  })
}

# Notification History Table - tracks sent notifications for rate limiting
resource "aws_dynamodb_table" "notification_history" {
  name           = "${local.name_prefix}-NotificationHistory"
  billing_mode   = local.table_config.billing_mode
  hash_key       = "userId"
  range_key      = "timestamp"
  
  attribute {
    name = "userId"
    type = "S"
  }
  
  attribute {
    name = "timestamp"
    type = "S"
  }
  
  attribute {
    name = "type"
    type = "S"
  }
  
  # GSI for notification type rate limiting
  global_secondary_index {
    name               = "userId-type-index"
    hash_key           = "userId"
    range_key          = "type"
    projection_type    = "ALL"
  }
  
  # TTL for automatic cleanup of old history records (30 days)
  ttl {
    attribute_name = "ttl"
    enabled        = true
  }
  
  deletion_protection_enabled = local.table_config.deletion_protection
  
  point_in_time_recovery {
    enabled = local.table_config.point_in_time_recovery
  }
  
  tags = merge(var.tags, {
    Name = "${local.name_prefix}-NotificationHistory"
    Type = "Rate Limiting"
  })
}

# IAM Role for Notification Lambda Function
resource "aws_iam_role" "notification_lambda_role" {
  name = "${local.name_prefix}-notification-lambda-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# IAM Policy for Notification Lambda
resource "aws_iam_role_policy" "notification_lambda_policy" {
  name = "${local.name_prefix}-notification-lambda-policy"
  role = aws_iam_role.notification_lambda_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "dynamodb:GetItem",
          "dynamodb:PutItem",
          "dynamodb:UpdateItem",
          "dynamodb:DeleteItem",
          "dynamodb:Query",
          "dynamodb:Scan"
        ]
        Resource = [
          aws_dynamodb_table.notifications.arn,
          aws_dynamodb_table.device_tokens.arn,
          aws_dynamodb_table.notification_preferences.arn,
          aws_dynamodb_table.notification_history.arn,
          "${aws_dynamodb_table.notifications.arn}/index/*",
          "${aws_dynamodb_table.device_tokens.arn}/index/*",
          "${aws_dynamodb_table.notification_history.arn}/index/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "sns:Publish"
        ]
        Resource = aws_sns_topic.notifications.arn
      }
    ]
  })
}

# Attach basic execution role
resource "aws_iam_role_policy_attachment" "notification_lambda_basic" {
  role       = aws_iam_role.notification_lambda_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}
