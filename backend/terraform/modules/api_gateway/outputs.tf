# Outputs for API Gateway Module

# API Gateway outputs
output "api" {
  description = "API Gateway REST API object"
  value       = aws_api_gateway_rest_api.main
}

output "api_id" {
  description = "ID of the API Gateway"
  value       = aws_api_gateway_rest_api.main.id
}

output "api_arn" {
  description = "ARN of the API Gateway"
  value       = aws_api_gateway_rest_api.main.arn
}

output "api_execution_arn" {
  description = "Execution ARN of the API Gateway"
  value       = aws_api_gateway_rest_api.main.execution_arn
}

# Stage outputs (removed - no longer using stages)
# output "stage" {
#   description = "API Gateway stage object"
#   value       = aws_api_gateway_stage.main
# }

output "stage_name" {
  description = "Name of the API Gateway stage (default deployment)"
  value       = "default"
}

# output "stage_arn" {
#   description = "ARN of the API Gateway stage"
#   value       = aws_api_gateway_stage.main.arn
# }

# URL outputs
output "api_url" {
  description = "URL of the API Gateway"
  value       = var.domain_name != "" ? "https://${var.domain_name}" : "https://${aws_api_gateway_rest_api.main.id}.execute-api.${data.aws_region.current.name}.amazonaws.com"
}

output "invoke_url" {
  description = "Invoke URL of the API Gateway (default deployment)"
  value       = "https://${aws_api_gateway_rest_api.main.id}.execute-api.${data.aws_region.current.name}.amazonaws.com"
}

# Custom domain outputs (if created)
output "domain_name" {
  description = "Custom domain name object"
  value       = var.domain_name != "" ? aws_api_gateway_domain_name.main[0] : null
}

output "domain_name_regional_domain_name" {
  description = "Regional domain name of the custom domain"
  value       = var.domain_name != "" ? aws_api_gateway_domain_name.main[0].regional_domain_name : ""
}

output "domain_name_regional_zone_id" {
  description = "Regional zone ID of the custom domain"
  value       = var.domain_name != "" ? aws_api_gateway_domain_name.main[0].regional_zone_id : ""
}

# Authorizer outputs
output "authorizer" {
  description = "API Gateway authorizer object"
  value       = aws_api_gateway_authorizer.lambda_authorizer
}

output "authorizer_id" {
  description = "ID of the API Gateway authorizer"
  value       = aws_api_gateway_authorizer.lambda_authorizer.id
}

# CloudWatch Log Group outputs (if created)
output "log_group_name" {
  description = "Name of the API Gateway CloudWatch log group"
  value       = var.enable_logging ? aws_cloudwatch_log_group.api_gateway[0].name : ""
}

output "log_group_arn" {
  description = "ARN of the API Gateway CloudWatch log group"
  value       = var.enable_logging ? aws_cloudwatch_log_group.api_gateway[0].arn : ""
}


