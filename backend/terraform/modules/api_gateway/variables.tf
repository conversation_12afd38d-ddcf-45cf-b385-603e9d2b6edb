# Variables for API Gateway Module

variable "project_name" {
  description = "Name of the project"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "is_production_or_staging" {
  description = "Whether this is a production or staging environment"
  type        = bool
}

variable "domain_name" {
  description = "Custom domain name for API Gateway"
  type        = string
  default     = ""
}

variable "certificate_arn" {
  description = "ACM certificate ARN for custom domain"
  type        = string
  default     = ""
}

variable "enable_logging" {
  description = "Enable API Gateway access logging"
  type        = bool
  default     = true
}

variable "log_retention_days" {
  description = "CloudWatch log retention in days"
  type        = number
  default     = 14
}

# Dependencies
variable "authorizer_function" {
  description = "Authorizer Lambda function object"
  type        = any
}

variable "lambda_functions" {
  description = "Map of Lambda function objects"
  type        = any
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default     = {}
}
