# Authentication Module for GameFlex Backend - Cognito User Pool

locals {
  name_prefix = "${var.project_name}-${var.environment}"
}

# IAM Role for Cognito Custom Message Lambda
resource "aws_iam_role" "custom_message_lambda_role" {
  name = "${local.name_prefix}-cognito-custom-message-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# IAM Policy for Custom Message Lambda
resource "aws_iam_role_policy_attachment" "custom_message_lambda_basic" {
  role       = aws_iam_role.custom_message_lambda_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

# Lambda function for custom Cognito messages
resource "aws_lambda_function" "custom_message" {
  filename         = "${path.module}/custom-message-lambda.zip"
  function_name    = "${local.name_prefix}-cognito-custom-message"
  role            = aws_iam_role.custom_message_lambda_role.arn
  handler         = "index.handler"
  runtime         = var.lambda_runtime
  timeout         = 30

  source_code_hash = data.archive_file.custom_message_lambda.output_base64sha256

  environment {
    variables = {
      ENVIRONMENT = var.environment
    }
  }

  tags = merge(var.tags, {
    Name = "${local.name_prefix}-cognito-custom-message"
    Type = "Cognito Trigger"
  })
}

# Archive the Lambda function code with email templates
data "archive_file" "custom_message_lambda" {
  type        = "zip"
  output_path = "${path.module}/custom-message-lambda.zip"

  # Include the compiled TypeScript Lambda function
  source {
    content  = file("${path.root}/../src/cognito-triggers/custom-message.js")
    filename = "index.js"
  }

  # Include email templates
  source {
    content  = file("${path.root}/../email-templates/verification-email.html")
    filename = "email-templates/verification-email.html"
  }

  source {
    content  = file("${path.root}/../email-templates/verification-email.txt")
    filename = "email-templates/verification-email.txt"
  }

  source {
    content  = file("${path.root}/../email-templates/password-reset-email.html")
    filename = "email-templates/password-reset-email.html"
  }

  source {
    content  = file("${path.root}/../email-templates/password-reset-email.txt")
    filename = "email-templates/password-reset-email.txt"
  }

  source {
    content  = file("${path.root}/../email-templates/mfa-authentication-email.html")
    filename = "email-templates/mfa-authentication-email.html"
  }

  source {
    content  = file("${path.root}/../email-templates/mfa-authentication-email.txt")
    filename = "email-templates/mfa-authentication-email.txt"
  }
}

# Lambda permission for Cognito to invoke the function
resource "aws_lambda_permission" "cognito_custom_message" {
  statement_id  = "AllowCognitoInvoke"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.custom_message.function_name
  principal     = "cognito-idp.amazonaws.com"
  source_arn    = aws_cognito_user_pool.main.arn
}

# IAM Role for Cognito to send emails via SES
resource "aws_iam_role" "cognito_ses_role" {
  name = "${local.name_prefix}-cognito-ses-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "cognito-idp.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# IAM Policy for Cognito to send emails via SES
resource "aws_iam_role_policy" "cognito_ses_policy" {
  name = "${local.name_prefix}-cognito-ses-policy"
  role = aws_iam_role.cognito_ses_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ses:SendEmail",
          "ses:SendRawEmail"
        ]
        Resource = [
          "arn:aws:ses:${var.aws_region}:${var.aws_account_id}:identity/gameflex.io",
          "arn:aws:ses:${var.aws_region}:${var.aws_account_id}:identity/<EMAIL>"
        ]
      }
    ]
  })
}

# Cognito User Pool
resource "aws_cognito_user_pool" "main" {
  name = "${local.name_prefix}-users"

  # Auto verification settings
  auto_verified_attributes = var.enable_auto_verify_email ? ["email"] : []

  # Sign-in configuration - use email as username, not alias
  username_attributes = ["email"]

  # Username configuration
  username_configuration {
    case_sensitive = false
  }

  # Password policy
  password_policy {
    minimum_length                   = var.password_min_length
    require_uppercase                = true
    require_lowercase                = true
    require_numbers                  = true
    require_symbols                  = false
    temporary_password_validity_days = 7
  }

  # Account recovery
  account_recovery_setting {
    recovery_mechanism {
      name     = "verified_email"
      priority = 1
    }
  }

  # Lambda triggers
  lambda_config {
    custom_message = aws_lambda_function.custom_message.arn
  }

  # Email configuration
  email_configuration {
    email_sending_account  = "DEVELOPER"
    source_arn            = "arn:aws:ses:${var.aws_region}:${var.aws_account_id}:identity/gameflex.io"
    from_email_address    = "<EMAIL>"
    reply_to_email_address = "<EMAIL>"
    configuration_set     = null
  }

  # User pool add-ons
  user_pool_add_ons {
    advanced_security_mode = var.is_production_or_staging ? "ENFORCED" : "OFF"
  }

  # Deletion protection
  deletion_protection = var.is_production_or_staging ? "ACTIVE" : "INACTIVE"

  tags = merge(var.tags, {
    Name = "${local.name_prefix}-user-pool"
    Type = "Authentication"
  })
}

# Cognito User Pool Client
resource "aws_cognito_user_pool_client" "main" {
  name         = "${local.name_prefix}-client"
  user_pool_id = aws_cognito_user_pool.main.id

  # Client settings
  generate_secret = false

  # Auth flows
  explicit_auth_flows = [
    "ALLOW_ADMIN_USER_PASSWORD_AUTH",
    "ALLOW_USER_PASSWORD_AUTH",
    "ALLOW_USER_SRP_AUTH",
    "ALLOW_REFRESH_TOKEN_AUTH"
  ]

  # Token validity
  refresh_token_validity = var.refresh_token_validity_days
  access_token_validity  = var.token_validity_hours
  id_token_validity      = var.token_validity_hours

  token_validity_units {
    refresh_token = "days"
    access_token  = "hours"
    id_token      = "hours"
  }

  # Prevent user existence errors
  prevent_user_existence_errors = "ENABLED"

  # OAuth settings (for future use)
  supported_identity_providers = ["COGNITO"]
}

# Cognito User Pool Domain (optional, for hosted UI)
resource "aws_cognito_user_pool_domain" "main" {
  count        = var.create_user_pool_domain ? 1 : 0
  domain       = "${local.name_prefix}-auth"
  user_pool_id = aws_cognito_user_pool.main.id

  depends_on = [aws_cognito_user_pool.main]
}
