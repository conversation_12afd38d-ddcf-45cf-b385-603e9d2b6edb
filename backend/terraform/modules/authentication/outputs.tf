# Outputs for Authentication Module

# User Pool outputs
output "user_pool" {
  description = "Cognito User Pool object"
  value       = aws_cognito_user_pool.main
}

output "user_pool_id" {
  description = "ID of the Cognito User Pool"
  value       = aws_cognito_user_pool.main.id
}

output "user_pool_arn" {
  description = "ARN of the Cognito User Pool"
  value       = aws_cognito_user_pool.main.arn
}

output "user_pool_name" {
  description = "Name of the Cognito User Pool"
  value       = aws_cognito_user_pool.main.name
}

output "user_pool_endpoint" {
  description = "Endpoint of the Cognito User Pool"
  value       = aws_cognito_user_pool.main.endpoint
}

# User Pool Client outputs
output "user_pool_client" {
  description = "Cognito User Pool Client object"
  value       = aws_cognito_user_pool_client.main
}

output "user_pool_client_id" {
  description = "ID of the Cognito User Pool Client"
  value       = aws_cognito_user_pool_client.main.id
}

output "user_pool_client_name" {
  description = "Name of the Cognito User Pool Client"
  value       = aws_cognito_user_pool_client.main.name
}

# User Pool Domain outputs (if created)
output "user_pool_domain" {
  description = "Domain of the Cognito User Pool"
  value       = var.create_user_pool_domain ? aws_cognito_user_pool_domain.main[0].domain : ""
}

output "user_pool_domain_cloudfront_distribution_arn" {
  description = "CloudFront distribution ARN for the User Pool domain"
  value       = var.create_user_pool_domain ? aws_cognito_user_pool_domain.main[0].cloudfront_distribution_arn : ""
}

# Custom message Lambda outputs
output "custom_message_lambda_arn" {
  description = "ARN of the custom message Lambda function"
  value       = aws_lambda_function.custom_message.arn
}

output "custom_message_lambda_function_name" {
  description = "Name of the custom message Lambda function"
  value       = aws_lambda_function.custom_message.function_name
}
