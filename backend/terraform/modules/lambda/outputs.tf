# Outputs for Lambda Module

# All Lambda functions
output "functions" {
  description = "Map of all Lambda function objects"
  value       = aws_lambda_function.functions
}

output "function_names" {
  description = "Map of Lambda function names"
  value       = { for k, v in aws_lambda_function.functions : k => v.function_name }
}

output "function_arns" {
  description = "Map of Lambda function ARNs"
  value       = { for k, v in aws_lambda_function.functions : k => v.arn }
}

output "function_invoke_arns" {
  description = "Map of Lambda function invoke ARNs"
  value       = { for k, v in aws_lambda_function.functions : k => v.invoke_arn }
}

# Specific function outputs for API Gateway integration
output "authorizer_function" {
  description = "Authorizer Lambda function object"
  value       = aws_lambda_function.functions["authorizer"]
}

output "authorizer_function_arn" {
  description = "ARN of the authorizer Lambda function"
  value       = aws_lambda_function.functions["authorizer"].arn
}

output "authorizer_function_invoke_arn" {
  description = "Invoke ARN of the authorizer Lambda function"
  value       = aws_lambda_function.functions["authorizer"].invoke_arn
}

# IAM Role outputs
output "lambda_execution_role" {
  description = "Lambda execution IAM role object"
  value       = aws_iam_role.lambda_execution_role
}

output "lambda_execution_role_arn" {
  description = "ARN of the Lambda execution IAM role"
  value       = aws_iam_role.lambda_execution_role.arn
}

# CloudWatch Log Groups
output "log_group_names" {
  description = "Map of CloudWatch log group names"
  value       = { for k, v in aws_cloudwatch_log_group.lambda_logs : k => v.name }
}

output "log_group_arns" {
  description = "Map of CloudWatch log group ARNs"
  value       = { for k, v in aws_cloudwatch_log_group.lambda_logs : k => v.arn }
}
