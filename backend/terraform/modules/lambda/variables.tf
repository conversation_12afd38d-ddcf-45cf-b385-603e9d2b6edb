# Variables for Lambda Module

variable "project_name" {
  description = "Name of the project"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "is_production_or_staging" {
  description = "Whether this is a production or staging environment"
  type        = bool
}

variable "media_domain_name" {
  description = "Custom domain name for media CDN"
  type        = string
}

variable "runtime" {
  description = "Runtime for Lambda functions"
  type        = string
  default     = "nodejs22.x"
}

variable "timeout" {
  description = "Default timeout for Lambda functions in seconds"
  type        = number
  default     = 30
}

variable "memory_size" {
  description = "Default memory size for Lambda functions in MB"
  type        = number
  default     = 256
}

variable "log_retention_days" {
  description = "CloudWatch log retention in days"
  type        = number
  default     = 14
}

# Dependencies
variable "user_pool" {
  description = "Cognito User Pool object"
  type        = any
}

variable "user_pool_client" {
  description = "Cognito User Pool Client object"
  type        = any
}

variable "database_tables" {
  description = "Map of DynamoDB tables"
  type        = any
}

variable "media_bucket" {
  description = "S3 media bucket object"
  type        = any
}

variable "distribution" {
  description = "CloudFront distribution object"
  type        = any
}

variable "notification_stack" {
  description = "Notification stack outputs"
  type        = any
}

variable "utils_layer_arn" {
  description = "ARN of the utils Lambda layer"
  type        = string
}

# Secrets
variable "app_config_secret" {
  description = "Name of the app configuration secret"
  type        = string
}

variable "apple_config_secret" {
  description = "Name of the Apple configuration secret"
  type        = string
}

variable "xbox_config_secret" {
  description = "Name of the Xbox configuration secret"
  type        = string
}

variable "twitch_config_secret" {
  description = "Name of the Twitch configuration secret"
  type        = string
}

variable "kick_config_secret" {
  description = "Name of the Kick configuration secret"
  type        = string
}

variable "jwt_secret" {
  description = "Name of the JWT secret"
  type        = string
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default     = {}
}
