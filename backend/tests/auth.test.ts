import { getHttpClient } from './utils/http-client';
import { TestContext } from './utils/test-context';
import {
  SignUpRequest,
  SignUpResponse,
  SignInRequest,
  SignInResponse,
  SetUsernameRequest,
  SetUsernameResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  ValidateTokenResponse,
  TestUser
} from './types/api.types';

describe('Auth API Tests', () => {
  let httpClient: ReturnType<typeof getHttpClient>;
  let testUser: TestUser;

  beforeAll(() => {
    // Initialize HTTP client if not already done
    try {
      httpClient = getHttpClient();
    } catch (error) {
      // Initialize if not already initialized
      const { initializeHttpClient } = require('./utils/http-client');
      const { testConfig } = require('./config/test.config');
      httpClient = initializeHttpClient({
        baseURL: testConfig.apiBaseUrl,
        timeout: testConfig.timeout,
      });
    }

    testUser = TestContext.createTestUserData();
    if (process.env.VERBOSE_TESTS === 'true') {
      console.log('🔐 Starting Auth API tests...');
    }
  });

  describe('POST /auth/signup', () => {
    it('should create a new user successfully', async () => {

      const signupData: SignUpRequest = {
        email: testUser.email,
        password: testUser.password,
        firstName: testUser.firstName,
        lastName: testUser.lastName,
      };

      const response = await httpClient.post<SignUpResponse>('/auth/signup', signupData);

      expect(response).toBeValidApiResponse();
      expect(response.status).toBe(201);
      expect(response.data.message).toBe('User created successfully');
      expect(response.data.user).toBeDefined();
      expect(response.data.user.email).toBe(testUser.email);
      expect(response.data.user.firstName).toBe(testUser.firstName);
      expect(response.data.user.lastName).toBe(testUser.lastName);
      expect(response.data.user.id).toBeDefined();
      // Username should not be present in signup response since it's not required

      // Update test user with the returned ID
      testUser.id = response.data.user.id;
    });

    it('should return 400 when required fields are missing', async () => {
      const invalidData = {
        email: testUser.email,
        // Missing password and username
      };

      try {
        await httpClient.post('/auth/signup', invalidData);
        fail('Expected request to fail');
      } catch (error: any) {
        expect(error.response.status).toBe(400);
        // API Gateway validation may return different message formats
        expect(error.response.data.message || error.response.data.error).toBeDefined();
      }
    });

    it('should return error when trying to create duplicate user', async () => {
      const duplicateData: SignUpRequest = {
        email: testUser.email, // Same email as before
        password: 'AnotherPassword123!',
        firstName: 'Another',
        lastName: 'User',
      };

      try {
        await httpClient.post('/auth/signup', duplicateData);
        fail('Expected request to fail');
      } catch (error: any) {
        expect(error.response.status).toBeGreaterThanOrEqual(400);
        expect(error.response.data.error).toBeDefined();
      }
    });
  });

  describe('POST /auth/signin', () => {
    it('should sign in the user successfully', async () => {

      const signinData: SignInRequest = {
        email: testUser.email,
        password: testUser.password,
      };

      const response = await httpClient.post<SignInResponse>('/auth/signin', signinData);

      expect(response).toBeValidApiResponse();
      expect(response.status).toBe(200);
      expect(response.data.message).toBe('Sign in successful');
      expect(response.data.requiresUsername).toBe(true);
      expect(response.data.tokens).toBeDefined();
      expect(response.data.tokens.accessToken).toBeDefined();
      expect(response.data.tokens.refreshToken).toBeDefined();
      expect(response.data.tokens.idToken).toBeDefined();
      expect(response.data.user).toBeDefined();
      expect(response.data.user.email).toBe(testUser.email);
      expect(response.data.user.username).toBeUndefined();

      // Store tokens for use in other tests
      testUser.tokens = response.data.tokens;
      TestContext.setTestUser(testUser);

      // Set auth token for subsequent requests
      httpClient.setAuthToken(testUser.tokens.accessToken);


    });

    it('should return 400 when credentials are missing', async () => {
      const invalidData = {
        email: testUser.email,
        // Missing password
      };

      try {
        await httpClient.post('/auth/signin', invalidData);
        fail('Expected request to fail');
      } catch (error: any) {
        expect(error.response.status).toBe(400);
        // API Gateway validation may return different message formats
        expect(error.response.data.message || error.response.data.error).toBeDefined();
      }
    });

    it('should return 401 when credentials are invalid', async () => {
      const invalidData: SignInRequest = {
        email: testUser.email,
        password: 'WrongPassword123!',
      };

      try {
        await httpClient.post('/auth/signin', invalidData);
        fail('Expected request to fail');
      } catch (error: any) {
        expect(error.response.status).toBe(401);
        expect(error.response.data.error).toBe('Invalid email or password');
      }
    });
  });

  describe('POST /auth/set-username', () => {
    beforeEach(() => {
      // Ensure authentication is set up before running authenticated tests
      if (!httpClient.getAuthToken()) {
        throw new Error('AUTHENTICATION REQUIRED: No auth token found. This test requires authentication to be set up first.');
      }
    });

    it('should set username for authenticated user successfully', async () => {
      const setUsernameData: SetUsernameRequest = {
        username: testUser.username,
      };

      const response = await httpClient.post<SetUsernameResponse>('/auth/set-username', setUsernameData);

      expect(response).toBeValidApiResponse();
      expect(response.status).toBe(200);
      expect(response.data.message).toBe('Username set successfully');
      expect(response.data.user).toBeDefined();
      expect(response.data.user.email).toBe(testUser.email);
      expect(response.data.user.username).toBe(testUser.username);
      expect(response.data.user.firstName).toBe(testUser.firstName);
      expect(response.data.user.lastName).toBe(testUser.lastName);
      expect(response.data.user.id).toBeDefined();
    });

    it('should return 409 when username is already set for user', async () => {
      const duplicateUsernameData: SetUsernameRequest = {
        username: testUser.username, // Same username as already set
      };

      try {
        await httpClient.post('/auth/set-username', duplicateUsernameData);
        fail('Expected request to fail');
      } catch (error: any) {
        expect(error.response.status).toBe(409);
        expect(error.response.data.error).toBe('Username already set');
      }
    });

    it('should return 400 when username is invalid', async () => {
      const invalidUsernameData: SetUsernameRequest = {
        username: 'ab', // Too short
      };

      try {
        await httpClient.post('/auth/set-username', invalidUsernameData);
        fail('Expected request to fail');
      } catch (error: any) {
        expect(error.response.status).toBe(400);
        // Username validation returns specific error message
        expect(error.response.data.message || error.response.data.error).toMatch(/Invalid username|Invalid request body/);
      }
    });

    it('should sign in successfully after username is set', async () => {
      const signinData: SignInRequest = {
        email: testUser.email,
        password: testUser.password,
      };

      const response = await httpClient.post<SignInResponse>('/auth/signin', signinData);

      expect(response).toBeValidApiResponse();
      expect(response.status).toBe(200);
      expect(response.data.message).toBe('Sign in successful');
      expect(response.data.requiresUsername).toBeUndefined();
      expect(response.data.tokens).toBeDefined();
      expect(response.data.tokens.accessToken).toBeDefined();
      expect(response.data.tokens.refreshToken).toBeDefined();
      expect(response.data.tokens.idToken).toBeDefined();
      expect(response.data.user).toBeDefined();
      expect(response.data.user.email).toBe(testUser.email);
      expect(response.data.user.username).toBe(testUser.username);

      // Update tokens for subsequent tests
      testUser.tokens = response.data.tokens;
      TestContext.setTestUser(testUser);
      httpClient.setAuthToken(testUser.tokens.accessToken);
    });
  });

  describe('GET /auth/validate', () => {
    beforeEach(() => {
      // Ensure authentication is set up before running authenticated tests
      if (!httpClient.getAuthToken()) {
        throw new Error('AUTHENTICATION REQUIRED: No auth token found. This test requires authentication to be set up first.');
      }
    });

    it('should validate the access token successfully', async () => {

      const response = await httpClient.get<ValidateTokenResponse>('/auth/validate');

      expect(response).toBeValidApiResponse();
      expect(response.status).toBe(200);
      expect(response.data.message).toBe('Token is valid');
      expect(response.data.valid).toBe(true);
      expect(response.data.user).toBeDefined();
      expect(response.data.user!.email).toBe(testUser.email);


    });

    it('should return 401 when no token is provided', async () => {
      // Temporarily remove auth token
      httpClient.removeAuthToken();

      try {
        await httpClient.get('/auth/validate');
        fail('Expected request to fail');
      } catch (error: any) {
        // API Gateway may return 400 for missing required headers
        expect([400, 401]).toContain(error.response.status);
        expect(error.response.data.message || error.response.data.error).toBeDefined();
      }

      // Restore auth token
      httpClient.setAuthToken(TestContext.getAccessToken());
    });
  });

  describe('POST /auth/refresh', () => {
    beforeEach(() => {
      // Ensure authentication is set up before running authenticated tests
      if (!TestContext.getRefreshToken()) {
        throw new Error('AUTHENTICATION REQUIRED: No refresh token found. This test requires authentication to be set up first.');
      }
    });

    it('should refresh tokens successfully', async () => {

      const refreshData: RefreshTokenRequest = {
        refreshToken: TestContext.getRefreshToken(),
      };

      const response = await httpClient.post<RefreshTokenResponse>('/auth/refresh', refreshData);

      expect(response).toBeValidApiResponse();
      expect(response.status).toBe(200);
      expect(response.data.message).toBe('Token refreshed successfully');
      expect(response.data.tokens).toBeDefined();
      expect(response.data.tokens.accessToken).toBeDefined();
      expect(response.data.tokens.idToken).toBeDefined();

      // Update tokens in test context
      TestContext.updateTokens({
        accessToken: response.data.tokens.accessToken,
        idToken: response.data.tokens.idToken,
      });

      // Update HTTP client with new token
      httpClient.setAuthToken(response.data.tokens.accessToken);


    });

    it('should return 400 when refresh token is missing', async () => {
      try {
        await httpClient.post('/auth/refresh', {});
        fail('Expected request to fail');
      } catch (error: any) {
        expect(error.response.status).toBe(400);
        // API Gateway validation returns 'message' field, not 'error'
        expect(error.response.data.message || error.response.data.error).toBeDefined();
      }
    });
  });

  afterAll(() => {
    TestContext.setSetupComplete();
  });
});
