#!/usr/bin/env ts-node

import * as dotenv from 'dotenv';
import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';

// Load environment variables
dotenv.config({ path: '.env.test' });

const API_BASE_URL = process.env.TEST_API_BASE_URL || 'https://dev.api.gameflex.io';

interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  idToken: string;
  user: {
    id: string;
    email: string;
    username?: string;
  };
}

interface UploadResponse {
  message: string;
  mediaId: string;
  uploadUrl: string;
  media: {
    id: string;
    fileName: string;
    fileType: string;
    fileSize: number;
    mediaType: string;
    userId: string;
    status: string;
    staging_s3_key: string;
    final_s3_key: string;
    final_url: string;
    url: string;
  };
}

interface MediaStatusResponse {
  media: {
    id: string;
    fileName: string;
    fileType: string;
    fileSize: number;
    mediaType: string;
    userId: string;
    status: string;
    url: string;
    createdAt: string;
    updatedAt: string;
  };
}

async function authenticateTestUser(): Promise<string> {
  console.log('🔐 Authenticating test user...');
  
  const testEmail = `test-video-${Date.now()}@example.com`;
  const testPassword = 'TestPassword123!';
  
  try {
    // First, try to sign up
    console.log('📝 Signing up test user...');
    await axios.post(`${API_BASE_URL}/auth/signup`, {
      email: testEmail,
      password: testPassword,
      firstName: 'Test',
      lastName: 'User'
    });
    
    // Wait a moment for signup to process
    await new Promise(resolve => setTimeout(resolve, 1000));
  } catch (error: any) {
    // User might already exist, that's okay
    console.log('ℹ️ Signup failed (user might exist):', error.response?.status);
  }
  
  // Sign in
  console.log('🔑 Signing in...');
  const signInResponse = await axios.post(`${API_BASE_URL}/auth/signin`, {
    email: testEmail,
    password: testPassword
  });
  
  const authData: AuthResponse = signInResponse.data;
  console.log('✅ Authentication successful');
  return authData.accessToken;
}

async function testVideoUpload(accessToken: string): Promise<void> {
  console.log('\n🎥 Testing video upload and processing...');
  
  // Create a mock video file
  const mockVideoBuffer = Buffer.from('mock video content for testing - this simulates an MP4 file');
  const videoData = {
    fileName: 'test_gameplay.mp4',
    fileType: 'video/mp4',
    fileSize: mockVideoBuffer.length,
    mediaType: 'video'
  };
  
  console.log(`📁 Mock video: ${videoData.fileName} (${videoData.fileSize} bytes)`);
  
  // Step 1: Get presigned upload URL
  console.log('📤 Step 1: Requesting presigned upload URL...');
  const uploadResponse = await axios.post(`${API_BASE_URL}/media/upload`, videoData, {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    }
  });
  
  const uploadData: UploadResponse = uploadResponse.data;
  console.log(`✅ Step 1 complete: Got upload URL for media ID: ${uploadData.mediaId}`);
  console.log(`📋 Media type: ${uploadData.media.mediaType}`);
  console.log(`📋 Initial status: ${uploadData.media.status}`);
  
  // Step 2: Upload the mock video to S3
  console.log('📤 Step 2: Uploading video to S3...');
  const s3UploadResponse = await axios.put(uploadData.uploadUrl, mockVideoBuffer, {
    headers: {
      'Content-Type': videoData.fileType,
    },
  });
  
  console.log(`✅ Step 2 complete: Video uploaded to S3 (status: ${s3UploadResponse.status})`);
  
  // Step 3: Wait for processing and check status
  console.log('🤖 Step 3: Waiting for video processing...');
  
  let attempts = 0;
  const maxAttempts = 30; // 30 attempts with 2-second intervals = 1 minute max
  
  while (attempts < maxAttempts) {
    attempts++;
    
    try {
      const statusResponse = await axios.get(`${API_BASE_URL}/media/${uploadData.mediaId}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });
      
      const statusData: MediaStatusResponse = statusResponse.data;
      console.log(`📊 Attempt ${attempts}: Status = ${statusData.media.status}`);
      
      if (statusData.media.status === 'approved') {
        console.log('✅ Step 3 complete: Video auto-approved successfully!');
        console.log(`🔗 Final URL: ${statusData.media.url}`);
        console.log('🎉 Video processing test PASSED!');
        return;
      } else if (statusData.media.status === 'rejected_inappropriate') {
        console.log('❌ Video was rejected as inappropriate');
        console.log('🚨 This indicates the video processing fix did NOT work');
        throw new Error('Video should have been auto-approved but was rejected');
      } else if (statusData.media.status === 'failed') {
        console.log('❌ Video processing failed');
        throw new Error('Video processing failed');
      }
      
      // Still processing, wait and try again
      await new Promise(resolve => setTimeout(resolve, 2000));
      
    } catch (error: any) {
      console.log(`⚠️ Attempt ${attempts}: Error checking status:`, error.response?.status);
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  throw new Error('Video processing did not complete within expected time');
}

async function main() {
  try {
    console.log('🧪 Video Processing Test');
    console.log('========================');
    console.log(`🌐 API Base URL: ${API_BASE_URL}`);
    
    // Authenticate
    const accessToken = await authenticateTestUser();
    
    // Test video upload
    await testVideoUpload(accessToken);
    
    console.log('\n🎉 All tests passed! Video processing is working correctly.');
    console.log('📝 Summary: Videos are now auto-approved without content analysis.');
    
  } catch (error: any) {
    console.error('\n❌ Test failed:', error.message);
    if (error.response) {
      console.error('📋 Response status:', error.response.status);
      console.error('📋 Response data:', JSON.stringify(error.response.data, null, 2));
    }
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  main();
}

export { main as testVideoProcessing };
