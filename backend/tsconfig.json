{"compilerOptions": {"target": "ES2022", "module": "NodeNext", "moduleResolution": "NodeNext", "lib": ["es2022"], "declaration": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": false, "inlineSourceMap": true, "inlineSources": true, "experimentalDecorators": true, "strictPropertyInitialization": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "isolatedModules": true, "skipLibCheck": true, "typeRoots": ["./node_modules/@types", "./types"], "baseUrl": ".", "paths": {"/opt/nodejs/*": ["src/layers/utils-layer/nodejs/*"]}}, "include": ["src/**/*.ts", "types/**/*"], "exclude": ["node_modules", "cdk.out", "cloudflare", "src/*/node_modules"]}