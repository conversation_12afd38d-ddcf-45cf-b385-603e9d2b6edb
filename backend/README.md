# GameFlex Backend - Terraform

A serverless backend for the GameFlex mobile application built with Terraform for infrastructure management.

## 🏗️ Architecture

This backend uses Terraform to deploy and manage:

- **AWS Lambda Functions** - Serverless compute for API endpoints
- **Amazon API Gateway** - REST API with custom authorizers
- **Amazon DynamoDB** - NoSQL database for application data
- **Amazon Cognito** - User authentication and management
- **AWS Secrets Manager** - Secure configuration storage
- **Amazon S3** - Object storage for media files with CloudFront CDN
- **AWS Rekognition** - AI-powered content moderation and analysis
- **AWS SNS** - Push notification services

## 🔄 Migration from CDK to Terraform

This project has been migrated from AWS CDK to Terraform for better infrastructure management and team collaboration. Key changes:

- **Infrastructure as Code**: CDK TypeScript → Terraform HCL
- **State Management**: CDK managed → Terraform S3 backend with DynamoDB locking
- **Deployment**: `deploy.sh` → `terraform-deploy.sh`
- **Modularity**: CDK nested stacks → Terraform modules
- **Environment Management**: Improved with `.tfvars` files

For detailed migration information, see [terraform/README.md](terraform/README.md).

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (v18 or later) - [Download here](https://nodejs.org/)
- **npm** (comes with Node.js)
- **AWS CLI** (v2 or later) - [Installation guide](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html)
- **Terraform** (v1.0 or later) - [Download here](https://www.terraform.io/downloads.html)
- **jq** - For JSON parsing - `brew install jq` (macOS) or equivalent
- **Git** - [Download here](https://git-scm.com/)

### Install Terraform

```bash
# macOS with Homebrew
brew install terraform

# Or download from https://www.terraform.io/downloads.html
```

### Configure AWS Credentials

```bash
aws configure
```

Or set environment variables:
```bash
export AWS_ACCESS_KEY_ID=your_access_key
export AWS_SECRET_ACCESS_KEY=your_secret_key
export AWS_DEFAULT_REGION=us-west-2
```

### Terraform State Backend Setup

Before first deployment, create S3 buckets and DynamoDB tables for Terraform state:

```bash
# Development environment
aws s3 mb s3://gameflex-terraform-state-development --region us-west-2
aws dynamodb create-table \
    --table-name gameflex-terraform-locks-development \
    --attribute-definitions AttributeName=LockID,AttributeType=S \
    --key-schema AttributeName=LockID,KeyType=HASH \
    --provisioned-throughput ReadCapacityUnits=5,WriteCapacityUnits=5 \
    --region us-west-2
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
npm install
```

### 2. Set Up Environment Variables

Create a `.env` file in the backend directory:

```bash
# Copy the example file
cp .env.example .env

# Edit the file with your values
nano .env
```

### 3. Deploy to AWS

```bash
# Deploy to development environment (default)
./terraform-deploy.sh

# Or specify environment explicitly
./terraform-deploy.sh development
./terraform-deploy.sh staging
./terraform-deploy.sh production

# Show plan before applying
./terraform-deploy.sh development --plan

# Auto-approve deployment
./terraform-deploy.sh development -y
```

## 📁 Project Structure

```
backend/
├── terraform/              # Terraform infrastructure code
│   ├── main.tf             # Main Terraform configuration
│   ├── variables.tf        # Input variables
│   ├── outputs.tf          # Output values
│   ├── environments/       # Environment-specific configurations
│   ├── backend-configs/    # Terraform state backend configs
│   └── modules/            # Terraform modules
│       ├── database/       # DynamoDB tables
│       ├── authentication/ # Cognito User Pool
│       ├── storage/        # S3 and CloudFront
│       ├── notifications/  # SNS and notification tables
│       ├── landing_page/   # Static website hosting
│       ├── lambda/         # Lambda functions
│       └── api_gateway/    # API Gateway and routes
├── src/                    # Lambda function source code
│   ├── auth/              # Authentication functions
│   ├── authorizer/        # API Gateway authorizer
│   ├── channels/          # Channel management
│   ├── health/            # Health check endpoint
│   ├── media/             # Media upload/management
│   ├── posts/             # Post management
│   ├── reflexes/          # Reflex (reaction) system
│   ├── users/             # User management
│   └── utils/             # Shared utilities
├── tests/                  # Test files
├── terraform-deploy.sh     # Terraform deployment script
└── package.json           # Node.js dependencies
```

## 🛠️ Development Commands

### Build and Compilation

```bash
npm run build          # Compile TypeScript
npm run watch          # Watch for changes and compile
```

### Testing

```bash
npm test               # Run all tests
npm run test:watch     # Run tests in watch mode
npm run test:coverage  # Run tests with coverage
npm run test:unit      # Run unit tests only
```

### Terraform Commands

```bash
# Navigate to terraform directory for direct commands
cd terraform

terraform plan         # Show planned changes
terraform apply        # Apply changes
terraform destroy      # Destroy infrastructure
terraform output       # Show outputs
```

## 🚀 Deployment

The project uses a Terraform-based deployment script that supports all environments. Development is the default environment.

### Development Environment (Default)

```bash
./terraform-deploy.sh
# or
./terraform-deploy.sh development
```

### Staging Environment

```bash
./terraform-deploy.sh staging
```

### Production Environment

```bash
./terraform-deploy.sh production
```

### Deployment Options

The Terraform deployment script supports the following options:

- `-h, --help` - Show help message
- `-y, --yes` - Skip confirmation prompts (auto-approve)
- `-p, --plan` - Show Terraform plan before applying
- `--plan-only` - Only show plan, don't apply
- `--destroy` - Destroy infrastructure instead of creating
- `-v, --verbose` - Enable verbose output

Examples:
```bash
./deploy.sh --diff              # Show changes before deploying to development
./deploy.sh staging -y          # Deploy to staging without confirmation
./deploy.sh production --diff   # Show changes before deploying to production
./deploy.sh --synth-only        # Only synthesize, don't deploy
```

## 🔧 Configuration

### Environment Variables

The CDK stack uses context variables for configuration:

- `environment` - Target environment (development, staging, production)
- `projectName` - Project name prefix for resources

### Lambda Function Environment Variables

Each Lambda function receives these environment variables:

- `ENVIRONMENT` - Current environment
- `PROJECT_NAME` - Project name
- `USER_POOL_ID` - Cognito User Pool ID
- `USER_POOL_CLIENT_ID` - Cognito User Pool Client ID
- `USERS_TABLE` - Users DynamoDB table name
- `POSTS_TABLE` - Posts DynamoDB table name
- `MEDIA_TABLE` - Media DynamoDB table name
- `R2_SECRET_NAME` - CloudFlare R2 configuration secret name
- `APP_CONFIG_SECRET_NAME` - Application configuration secret name

### Secrets Manager Configuration

The stack creates two secrets in AWS Secrets Manager:

1. **R2 Configuration** (`{projectName}-r2-config-{environment}`)
   ```json
   {
     "accountId": "your_cloudflare_account_id",
     "accessKeyId": "your_r2_access_key",
     "secretAccessKey": "your_r2_secret_key",
     "endpoint": "https://your_account_id.r2.cloudflarestorage.com",
     "bucketName": "your-bucket-name",
     "publicUrl": "https://your-public-domain.com"
   }
   ```

2. **App Configuration** (`{projectName}-app-config-{environment}`)
   ```json
   {
     "jwtSecret": "your_jwt_secret_key",
     "apiVersion": "v1",
     "corsOrigins": ["https://your-frontend-domain.com"]
   }
   ```

## 🗄️ Database Schema

### DynamoDB Tables

The stack creates the following DynamoDB tables:

1. **Users** - User account information
2. **UserProfiles** - Extended user profile data
3. **Posts** - User posts and content
4. **Media** - Media file metadata
5. **Comments** - Post comments
6. **Likes** - Post and comment likes
7. **Follows** - User follow relationships
8. **Channels** - Community channels
9. **ChannelMembers** - Channel membership
10. **Reflexes** - User reactions/reflexes

### Global Secondary Indexes (GSI)

- **Posts**: `user-id-index`, `channel-id-index`, `created-at-index`
- **Media**: `user-id-index`, `post-id-index`
- **Comments**: `post-id-index`, `user-id-index`
- **Likes**: `user-id-index`, `post-id-index`
- **Follows**: `follower-id-index`, `following-id-index`
- **ChannelMembers**: `user-id-index`, `channel-id-index`

## 🔐 Authentication

The backend uses Amazon Cognito for user authentication with:

- **User Pool** - Manages user accounts and authentication
- **User Pool Client** - Configured for mobile app integration
- **API Gateway Authorizer** - Custom Lambda authorizer for JWT validation

### Authentication Flow

1. Users sign up/sign in through Cognito
2. Cognito returns JWT tokens
3. Mobile app includes JWT in API requests
4. API Gateway authorizer validates JWT
5. Lambda functions receive user context

## 🌐 API Endpoints

### Base URL Structure

- **Development**: `https://dev.api.gameflex.io`
- **Staging**: `https://staging.api.gameflex.io`
- **Production**: `https://api.gameflex.io`

### Available Endpoints

- `GET /health` - Health check (no auth required)
- `POST /auth/signup` - User registration
- `POST /auth/signin` - User login
- `POST /auth/signout` - User logout
- `GET /auth/me` - Get current user info
- `GET /users/{id}` - Get user profile
- `PUT /users/{id}` - Update user profile
- `GET /posts` - List posts
- `POST /posts` - Create post
- `GET /posts/{id}` - Get specific post
- `PUT /posts/{id}` - Update post
- `DELETE /posts/{id}` - Delete post
- `GET /channels` - List channels
- `POST /channels` - Create channel
- `GET /media/upload-url` - Get presigned upload URL
- `POST /media` - Create media record

## 🧪 Testing

### Unit Tests

Run CDK unit tests to validate infrastructure:

```bash
npm test
```

### Integration Tests

Test against deployed infrastructure:

```bash
npm run test:integration
```

### API Testing

Test API endpoints manually:

```bash
# Health check
curl https://your-api-url/health

# With authentication
curl -H "Authorization: Bearer your-jwt-token" \
     https://your-api-url/auth/me
```

## 🔍 Monitoring and Logging

### CloudWatch Logs

Lambda function logs are automatically sent to CloudWatch Logs:

- Log Group: `/aws/lambda/{function-name}`
- Retention: 14 days (configurable per environment)

### Monitoring Stack Outputs

After deployment, get important information:

```bash
aws cloudformation describe-stacks \
  --stack-name gameflex-development \
  --query 'Stacks[0].Outputs' \
  --output table
```

## 🚨 Troubleshooting

### Common Issues

#### 1. CDK Bootstrap Required

**Error**: `Need to perform AWS CDK bootstrap`

**Solution**:
```bash
cdk bootstrap aws://ACCOUNT-NUMBER/REGION
```

#### 2. Insufficient Permissions

**Error**: `User is not authorized to perform: cloudformation:CreateStack`

**Solution**: Ensure your AWS user/role has the necessary permissions:
- CloudFormation full access
- IAM permissions for resource creation
- Service-specific permissions (Lambda, API Gateway, DynamoDB, etc.)

#### 3. Lambda Function Dependencies

**Error**: Lambda function fails to import modules

**Solution**: Ensure dependencies are installed in each Lambda directory:
```bash
cd src/auth && npm install
cd src/posts && npm install
# etc.
```

#### 4. Environment Variable Issues

**Error**: Lambda functions can't access environment variables

**Solution**: Check that environment variables are properly set in the CDK stack and Lambda functions are using the correct variable names.

### Debugging

#### Enable Verbose Logging

```bash
export CDK_DEBUG=true
./deploy.sh --verbose
# or for specific environment
./deploy.sh staging --verbose
```

#### Check Lambda Logs

```bash
aws logs tail /aws/lambda/gameflex-development-auth --follow
```

#### Validate CloudFormation Template

```bash
npm run synth
```

## 🔄 Migration from SAM

This project has been migrated from AWS SAM to AWS CDK. Key changes:

### What Changed

1. **Infrastructure as Code**: `template.yaml` → TypeScript CDK stacks
2. **Deployment**: `sam deploy` → `cdk deploy`
3. **Build Process**: `sam build` → `npm run build`
4. **Configuration**: `samconfig.toml` → CDK context and environment variables
5. **Testing**: SAM local → CDK unit tests + integration tests

### What Stayed the Same

1. **Lambda Functions**: Source code remains unchanged
2. **API Structure**: Same endpoints and request/response formats
3. **Database Schema**: DynamoDB table structures unchanged
4. **Authentication**: Cognito configuration preserved

### Migration Benefits

- **Type Safety**: TypeScript provides compile-time error checking
- **Better IDE Support**: IntelliSense and auto-completion
- **Programmatic Infrastructure**: More flexible than YAML templates
- **Easier Testing**: Built-in unit testing for infrastructure
- **Better Dependency Management**: npm ecosystem integration

## � Documentation

Comprehensive documentation is available in the `docs/` folder:

### Core Features
- **[Staging Workflow](docs/STAGING_WORKFLOW.md)** - Secure media upload and processing workflow
- **[Rekognition Integration](docs/REKOGNITION_INTEGRATION.md)** - AI-powered content moderation and analysis
- **[Review System](docs/REVIEW_SYSTEM.md)** - Internal review system for rejected content

### Setup & Configuration
- **[Configuration](docs/CONFIGURATION.md)** - Environment variables and secrets management
- **[Deployment](docs/DEPLOYMENT.md)** - Deployment guide and best practices
- **[Custom Domains](docs/CUSTOM_DOMAINS.md)** - Setting up custom domain names

### Development & Testing
- **[Testing](docs/TESTING.md)** - Testing strategies and test execution
- **[Seeding](docs/SEEDING.md)** - Database seeding and sample data
- **[Troubleshooting](docs/TROUBLESHOOTING.md)** - Common issues and solutions

### Operations
- **[Staging Environment](docs/STAGING.md)** - Staging environment management
- **[Request Validation](docs/request-validation.md)** - API request validation patterns

## �📚 Additional Resources

- [AWS CDK Documentation](https://docs.aws.amazon.com/cdk/)
- [AWS CDK TypeScript Reference](https://docs.aws.amazon.com/cdk/api/v2/typescript/)
- [AWS Lambda Documentation](https://docs.aws.amazon.com/lambda/)
- [Amazon API Gateway Documentation](https://docs.aws.amazon.com/apigateway/)
- [Amazon DynamoDB Documentation](https://docs.aws.amazon.com/dynamodb/)
- [Amazon Cognito Documentation](https://docs.aws.amazon.com/cognito/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Make your changes
4. Run tests: `npm test`
5. Commit your changes: `git commit -am 'Add new feature'`
6. Push to the branch: `git push origin feature/new-feature`
7. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:

1. Check the troubleshooting section above
2. Review AWS CDK documentation
3. Check existing GitHub issues
4. Create a new issue with detailed information

---

**GameFlex Backend** - Built with ❤️ using AWS CDK
