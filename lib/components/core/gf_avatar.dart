import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';
import '../../utils/display_name_utils.dart';

/// GameFlex avatar component
///
/// A reusable avatar component that handles different avatar states:
/// - Network image with fallback
/// - Initials fallback
/// - Loading state
/// - Error state
///
/// Example usage:
/// ```dart
/// GFAvatar(
///   imageUrl: user.avatarUrl,
///   displayName: user.displayName,
///   radius: 24,
///   onTap: () => _viewProfile(),
/// )
/// ```
class GFAvatar extends StatelessWidget {
  final String? imageUrl;
  final String displayName;
  final double radius;
  final VoidCallback? onTap;
  final Color? backgroundColor;
  final Color? textColor;
  final bool showBorder;
  final Color? borderColor;
  final double borderWidth;

  const GFAvatar({
    super.key,
    this.imageUrl,
    required this.displayName,
    this.radius = 20,
    this.onTap,
    this.backgroundColor,
    this.textColor,
    this.showBorder = false,
    this.borderColor,
    this.borderWidth = 2,
  });

  @override
  Widget build(BuildContext context) {
    final avatar = _buildAvatar();

    if (onTap != null) {
      return GestureDetector(onTap: onTap, child: avatar);
    }

    return avatar;
  }

  Widget _buildAvatar() {
    final backgroundImage = _getBackgroundImage();

    return Container(
      decoration:
          showBorder
              ? BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: borderColor ?? AppColors.gfGreen,
                  width: borderWidth,
                ),
              )
              : null,
      child:
          backgroundImage != null
              ? CircleAvatar(
                backgroundColor:
                    backgroundColor ?? AppColors.gfGreen.withValues(alpha: 77),
                radius: radius,
                backgroundImage: backgroundImage,
                onBackgroundImageError: (_, __) {},
              )
              : CircleAvatar(
                backgroundColor:
                    backgroundColor ?? AppColors.gfGreen.withValues(alpha: 77),
                radius: radius,
                child: _buildFallbackContent(),
              ),
    );
  }

  ImageProvider? _getBackgroundImage() {
    if (imageUrl != null && imageUrl!.isNotEmpty) {
      return NetworkImage(imageUrl!);
    }
    return null;
  }

  Widget? _buildFallbackContent() {
    // Always provide fallback content in case the image fails to load
    return Text(
      _getInitials(),
      style: TextStyle(
        color: textColor ?? AppColors.gfDarkBlue,
        fontWeight: FontWeight.bold,
        fontSize: radius * 0.6, // Scale font size with radius
      ),
    );
  }

  String _getInitials() {
    if (displayName.isEmpty) return 'U';

    // Parse the display name to extract just the username part (without provider brackets)
    final username = extractUsername(displayName);

    final words = username.trim().split(' ');
    if (words.length == 1) {
      return words[0][0].toUpperCase();
    } else {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    }
  }
}

/// A specialized avatar for online status indication
class GFAvatarWithStatus extends StatelessWidget {
  final String? imageUrl;
  final String displayName;
  final double radius;
  final VoidCallback? onTap;
  final bool isOnline;
  final Color? backgroundColor;
  final Color? textColor;

  const GFAvatarWithStatus({
    super.key,
    this.imageUrl,
    required this.displayName,
    this.radius = 20,
    this.onTap,
    this.isOnline = false,
    this.backgroundColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        GFAvatar(
          imageUrl: imageUrl,
          displayName: displayName,
          radius: radius,
          onTap: onTap,
          backgroundColor: backgroundColor,
          textColor: textColor,
        ),
        if (isOnline)
          Positioned(
            bottom: 0,
            right: 0,
            child: Container(
              width: radius * 0.4,
              height: radius * 0.4,
              decoration: BoxDecoration(
                color: AppColors.gfGreen,
                shape: BoxShape.circle,
                border: Border.all(
                  color: AppColors.gfDarkBackground100,
                  width: 2,
                ),
              ),
            ),
          ),
      ],
    );
  }
}

/// A group of overlapping avatars
class GFAvatarGroup extends StatelessWidget {
  final List<String?> imageUrls;
  final List<String> displayNames;
  final double radius;
  final int maxVisible;
  final VoidCallback? onTap;

  const GFAvatarGroup({
    super.key,
    required this.imageUrls,
    required this.displayNames,
    this.radius = 16,
    this.maxVisible = 3,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final visibleCount = maxVisible.clamp(1, imageUrls.length);
    final remainingCount = imageUrls.length - visibleCount;

    return GestureDetector(
      onTap: onTap,
      child: SizedBox(
        width: radius * 2 + (visibleCount - 1) * radius * 1.2,
        height: radius * 2,
        child: Stack(
          children: [
            // Show visible avatars
            for (int i = 0; i < visibleCount; i++)
              Positioned(
                left: i * radius * 1.2,
                child: GFAvatar(
                  imageUrl: imageUrls[i],
                  displayName: displayNames[i],
                  radius: radius,
                  showBorder: true,
                  borderColor: AppColors.gfDarkBackground100,
                ),
              ),
            // Show count indicator if there are more
            if (remainingCount > 0)
              Positioned(
                left: visibleCount * radius * 1.2,
                child: CircleAvatar(
                  radius: radius,
                  backgroundColor: AppColors.gfGrayText,
                  child: Text(
                    '+$remainingCount',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: radius * 0.5,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
