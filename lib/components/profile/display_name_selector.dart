import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../theme/app_theme.dart';
import '../../components/index.dart';
import '../../providers/auth_provider.dart';
import '../../providers/user_profile_provider.dart';
import '../../utils/display_name_utils.dart';
import '../../config/display_name_config.dart';
import '../../services/user_service.dart';

/// A widget that allows users to select their display name from available options
/// This can be embedded in profile screens or used as a standalone component
class DisplayNameSelector extends StatefulWidget {
  final bool showTitle;
  final VoidCallback? onDisplayNameChanged;

  const DisplayNameSelector({
    super.key,
    this.showTitle = true,
    this.onDisplayNameChanged,
  });

  @override
  State<DisplayNameSelector> createState() => _DisplayNameSelectorState();
}

class _DisplayNameSelectorState extends State<DisplayNameSelector> {
  List<DisplayNameOption> _options = [];
  DisplayNameOption? _selectedOption;
  bool _isLoading = true;
  bool _isUpdating = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadDisplayNameOptions();
  }

  Future<void> _loadDisplayNameOptions() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final user = authProvider.user;

      if (user == null) {
        setState(() {
          _errorMessage = 'User not found';
          _isLoading = false;
        });
        return;
      }

      final options = await UserService.getDisplayNameOptions(user.id);

      setState(() {
        _options = options;
        // Set current selection if user has a display name set
        if (user.displayName != null) {
          // Parse the display name to extract provider and username
          final parsed = parseDisplayName(user.displayName!);
          final provider = parsed['provider']!;
          final displayName = parsed['displayName']!;

          _selectedOption = options.firstWhere(
            (option) =>
                option.provider == provider &&
                option.displayName == displayName,
            orElse:
                () =>
                    options.isNotEmpty
                        ? options.first
                        : DisplayNameOption(
                          provider: DisplayNameConfig.defaultProvider,
                          displayName: user.username ?? 'User',
                          formattedDisplayName: user.username ?? 'User',
                          isAvailable: true,
                        ),
          );
        } else if (options.isNotEmpty) {
          _selectedOption = options.first;
        }
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load display name options: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _updateDisplayName() async {
    if (_selectedOption == null) return;

    try {
      setState(() {
        _isUpdating = true;
        _errorMessage = null;
      });

      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final user = authProvider.user;

      if (user == null) {
        setState(() {
          _errorMessage = 'User not found';
          _isUpdating = false;
        });
        return;
      }

      await UserService.updateDisplayName(
        user.id,
        _selectedOption!.provider,
        _selectedOption!.displayName,
      );

      // Clear the user profile cache to force refresh
      UserProfileProvider.clearUserCache(user.id);

      // Update the user in the auth provider
      final updatedUser = user.copyWith(
        displayName:
            _selectedOption!
                .formattedDisplayName, // Already includes provider prefix
      );
      authProvider.updateUser(updatedUser);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Display name updated successfully'),
            backgroundColor: AppColors.gfGreen,
          ),
        );
        widget.onDisplayNameChanged?.call();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to update display name: $e';
      });
    } finally {
      setState(() {
        _isUpdating = false;
      });
    }
  }

  Widget _buildOptionTile(DisplayNameOption option) {
    final isSelected = _selectedOption == option;

    return GFCard(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: _buildProviderIcon(option.provider),
        title: Text(
          option.formattedDisplayName,
          style: TextStyle(
            color: AppColors.gfOffWhite,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            fontSize: 14,
          ),
        ),
        subtitle: Text(
          getProviderDisplayName(option.provider),
          style: const TextStyle(color: AppColors.gfGrayText, fontSize: 12),
        ),
        trailing: Radio<DisplayNameOption>.adaptive(
          value: option,
          groupValue: _selectedOption,
          onChanged: (DisplayNameOption? value) {
            setState(() {
              _selectedOption = value;
            });
          },
          activeColor: AppColors.gfGreen,
        ),
        onTap: () {
          setState(() {
            _selectedOption = option;
          });
        },
      ),
    );
  }

  Widget _buildProviderIcon(String provider) {
    String? svgAsset;
    Color? iconColor;

    try {
      final providerEnum = DisplayNameProvider.fromString(provider);
      iconColor = Color(providerEnum.primaryColor);

      switch (providerEnum) {
        case DisplayNameProvider.xbox:
          svgAsset = 'assets/images/icons/third_party_auth/xbox.svg';
          break;
        case DisplayNameProvider.apple:
          // No SVG available, use material icon
          return Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: iconColor,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(Icons.apple, color: Colors.white, size: 20),
          );
        case DisplayNameProvider.twitch:
          svgAsset = 'assets/images/icons/third_party_auth/twitch.svg';
          break;
        case DisplayNameProvider.kick:
          svgAsset = 'assets/images/icons/third_party_auth/kick.svg';
          break;
        case DisplayNameProvider.gf:
          svgAsset = 'assets/images/icons/third_party_auth/gameflex.svg';
          break;
      }
    } catch (e) {
      // Handle additional providers that might not be in the enum but have SVG files
      switch (provider.toLowerCase()) {
        case 'apple':
          svgAsset = 'assets/images/icons/third_party_auth/apple.svg';
          iconColor = Colors.white;
          break;
        case 'google':
          svgAsset = 'assets/images/icons/third_party_auth/google.svg';
          iconColor = null; // Keep original colors
          break;
        default:
          // Fallback for unknown providers
          return Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: AppColors.gfGrayText,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(Icons.help_outline, color: Colors.white, size: 18),
          );
      }
    }

    // If we have an SVG asset, use it
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: iconColor?.withAlpha(51), // 0.2 opacity
        borderRadius: BorderRadius.circular(6),
      ),
      child: Padding(
        padding: const EdgeInsets.all(6),
        child: SvgPicture.asset(
          svgAsset,
          colorFilter:
              iconColor != null
                  ? ColorFilter.mode(iconColor, BlendMode.srcIn)
                  : null,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: AppColors.gfGreen),
      );
    }

    if (_errorMessage != null) {
      return Column(
        children: [
          Text(
            _errorMessage!,
            style: const TextStyle(color: Colors.red, fontSize: 14),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          GFButton(
            text: 'Retry',
            onPressed: _loadDisplayNameOptions,
            type: GFButtonType.secondary,
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showTitle) ...[
          const Text(
            'Display Name',
            style: TextStyle(
              color: AppColors.gfOffWhite,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Choose how your name appears to other users',
            style: TextStyle(color: AppColors.gfGrayText, fontSize: 14),
          ),
          const SizedBox(height: 16),
        ],

        // Options list
        ...(_options.take(3).map(_buildOptionTile).toList()),

        if (_options.length > 3) ...[
          const SizedBox(height: 8),
          Text(
            '${_options.length - 3} more options available',
            style: const TextStyle(color: AppColors.gfGrayText, fontSize: 12),
          ),
        ],

        const SizedBox(height: 16),

        // Update button
        SizedBox(
          width: double.infinity,
          child: GFButton(
            text: _isUpdating ? 'Updating...' : 'Update Display Name',
            onPressed:
                _isUpdating || _selectedOption == null
                    ? null
                    : _updateDisplayName,
            type: GFButtonType.primary,
            isEnabled: !_isUpdating && _selectedOption != null,
          ),
        ),
      ],
    );
  }
}
