import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:gameflex_mobile/theme/app_theme.dart';
import 'package:gameflex_mobile/components/index.dart';

import 'package:gameflex_mobile/providers/auth_provider.dart';
import 'package:gameflex_mobile/services/api_service.dart';

import 'package:gameflex_mobile/services/config_service.dart';
import 'package:gameflex_mobile/utils/app_logger.dart';
import 'xbox_account_choice_screen.dart';

import 'email_login_screen.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  bool _showEmailForm = false;

  /// Handle Twitch Sign In
  Future<void> _handleTwitchSignIn() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    developer.log('LoginScreen: Starting Twitch Sign In');
    AppLogger.auth('Starting Twitch Sign In');

    // Show loading indicator
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Signing in with Twitch...'),
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }

    // Clear any previous errors
    authProvider.clearError();

    final success = await authProvider.signInWithTwitch();

    developer.log('LoginScreen: Twitch Sign In result: $success');
    AppLogger.auth('Twitch Sign In result: $success');

    if (mounted) {
      if (success) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Twitch Sign In successful!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );
      } else {
        // Error handling is done by AuthProvider
        // Just log that we're done
        developer.log('LoginScreen: Twitch Sign In failed');
        AppLogger.auth('Twitch Sign In failed');
      }
    }
  }

  /// Handle Kick Sign In
  Future<void> _handleKickSignIn() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    developer.log('LoginScreen: Starting Kick Sign In');
    AppLogger.auth('Starting Kick Sign In');

    // Show loading indicator
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Signing in with Kick...'),
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }

    // Clear any previous errors
    authProvider.clearError();

    final success = await authProvider.signInWithKick();

    developer.log('LoginScreen: Kick Sign In result: $success');
    AppLogger.auth('Kick Sign In result: $success');

    if (mounted) {
      if (success) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Kick Sign In successful!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );
      } else {
        // Error handling is done by AuthProvider
        // Just log that we're done
        developer.log('LoginScreen: Kick Sign In failed');
        AppLogger.auth('Kick Sign In failed');
      }
    }
  }

  /// Handle Apple Sign In (temporarily disabled)
  /*
  Future<void> _handleAppleSignIn() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    developer.log('LoginScreen: Starting Apple Sign In');
    AppLogger.auth('Starting Apple Sign In');

    // Show loading indicator
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Signing in with Apple...'),
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }

    // Clear any previous errors
    authProvider.clearError();

    final success = await authProvider.signInWithApple();

    developer.log('LoginScreen: Apple Sign In result: $success');
    AppLogger.auth('Apple Sign In result: $success');

    if (mounted) {
      // Hide any existing snackbars
      ScaffoldMessenger.of(context).hideCurrentSnackBar();

      if (success) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Apple Sign In successful!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );
      } else {
        // Show error message
        final errorMessage = _formatErrorMessage(authProvider.errorMessage);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 6),
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }
  */

  Future<void> _testConnection() async {
    developer.log('LoginScreen: Testing AWS backend connection');
    AppLogger.info('Testing AWS backend connection');

    try {
      // Test AWS backend connection by making a simple API call
      final response = await ApiService.instance.makeRequest(
        method: 'GET',
        path: '/health',
      );

      final connectionTest = response.statusCode == 200;

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              connectionTest
                  ? 'AWS backend connection successful!'
                  : 'AWS backend connection failed - check backend',
            ),
            backgroundColor: connectionTest ? Colors.green : Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }

      developer.log(
        'LoginScreen: AWS backend connection test result: $connectionTest',
      );
      AppLogger.info('AWS backend connection test result: $connectionTest');
    } catch (e, stackTrace) {
      developer.log(
        'LoginScreen: AWS backend connection test error',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error('AWS BACKEND CONNECTION TEST ERROR', error: e);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Connection test failed: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  /// Handle Xbox Sign In
  Future<void> _handleXboxSignIn() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    developer.log('LoginScreen: Starting Xbox Sign In');
    AppLogger.auth('Starting Xbox Sign In');

    // Show loading indicator
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Signing in with Xbox...'),
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }

    // Clear any previous errors
    authProvider.clearError();

    final success = await authProvider.signInWithXbox();

    developer.log('LoginScreen: Xbox Sign In result: $success');
    AppLogger.auth('Xbox Sign In result: $success');

    if (mounted) {
      // Hide any existing snackbars
      ScaffoldMessenger.of(context).hideCurrentSnackBar();

      if (success) {
        // Check if account choice is required
        final accountChoiceData = authProvider.accountChoiceData;
        if (accountChoiceData != null) {
          // Navigate to account choice screen
          Navigator.of(context).push(
            MaterialPageRoute(
              builder:
                  (context) => XboxAccountChoiceScreen(
                    accountOptions:
                        accountChoiceData['accountOptions']
                            as Map<String, dynamic>,
                    xboxData:
                        accountChoiceData['xboxData'] as Map<String, dynamic>,
                  ),
            ),
          );
        } else {
          // Show success message for normal sign-in
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Xbox Sign In successful!'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      } else {
        // Error handling is done by AuthProvider
        // Just log that we're done
        developer.log('LoginScreen: Xbox Sign In failed');
        AppLogger.auth('Xbox Sign In failed');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Scaffold(
          backgroundColor: AppColors.gfGrayBlue900,
          resizeToAvoidBottomInset: true,
          floatingActionButton:
              ConfigService.instance.isDevelopment
                  ? FloatingActionButton(
                    onPressed: _testConnection,
                    backgroundColor: AppColors.gfGreen,
                    child: const Icon(Icons.wifi, color: Colors.black),
                  )
                  : null,
          floatingActionButtonLocation: FloatingActionButtonLocation.miniEndTop,
          body: Stack(
            children: [
              Container(
                decoration: const BoxDecoration(color: AppColors.gfGrayBlue900),
                child: SafeArea(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(horizontal: 24.0),
                    child: Column(
                      children: [
                        // GameFlex Logo - Fixed space at top
                        const SizedBox(height: 10),
                        Center(
                          child: SizedBox(
                            width: MediaQuery.of(context).size.width * 0.7,
                            height: 200,
                            child: SvgPicture.asset(
                              'assets/images/logos/logo_full_white.svg',
                              fit: BoxFit.contain,
                            ),
                          ),
                        ),
                        // Buttons section
                        const SizedBox(height: 20), // Reduced spacing

                        if (!_showEmailForm) ...[
                          // Gaming platforms section with border
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: AppColors.gfGreen.withValues(alpha: 0.3),
                                width: 1,
                              ),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Column(
                              children: [
                                // Gaming platforms label
                                const Text(
                                  'Gaming Platforms',
                                  style: TextStyle(
                                    color: AppColors.gfGreen,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                // Twitch login button
                                GFTwitchSignInButton(
                                  onPressed: _handleTwitchSignIn,
                                  height: 55,
                                  width:
                                      MediaQuery.of(context).size.width * 0.7,
                                ),
                                const SizedBox(height: 12),
                                // Kick login button
                                GFKickSignInButton(
                                  onPressed: _handleKickSignIn,
                                  height: 55,
                                  width:
                                      MediaQuery.of(context).size.width * 0.7,
                                ),
                                const SizedBox(height: 12),
                                // Xbox login button
                                GFXboxSignInButton(
                                  onPressed: _handleXboxSignIn,
                                  height: 55,
                                  width:
                                      MediaQuery.of(context).size.width * 0.7,
                                ),
                              ],
                            ),
                          ),
                        ],

                        // Email options that appear when button is clicked
                        if (_showEmailForm) ...[
                          const SizedBox(height: 10),
                          // Back button
                          Align(
                            alignment: Alignment.centerLeft,
                            child: TextButton.icon(
                              onPressed: () {
                                setState(() {
                                  _showEmailForm = false;
                                });
                              },
                              icon: const Icon(
                                Icons.arrow_back,
                                color: AppColors.gfGreen,
                              ),
                              label: const Text(
                                'Back',
                                style: TextStyle(color: AppColors.gfGreen),
                              ),
                            ),
                          ),
                          const SizedBox(height: 20),
                          // Sign Up button
                          GFEmailSignInButton(
                            text: 'Sign Up',
                            height: 55,
                            width: MediaQuery.of(context).size.width * 0.8,
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder:
                                      (context) => const EmailLoginScreen(
                                        isLogin: false,
                                      ),
                                ),
                              );
                            },
                          ),
                          const SizedBox(height: 16),
                          // Login button
                          GFEmailSignInButton(
                            text: 'Login',
                            height: 55,
                            width: MediaQuery.of(context).size.width * 0.8,
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder:
                                      (context) =>
                                          const EmailLoginScreen(isLogin: true),
                                ),
                              );
                            },
                          ),
                        ],

                        if (!_showEmailForm) ...[
                          // Social platforms section with border
                          const SizedBox(height: 20),
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: AppColors.gfBlue.withValues(alpha: 0.3),
                                width: 1,
                              ),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Column(
                              children: [
                                // Social platforms label
                                const Text(
                                  'Social Platforms',
                                  style: TextStyle(
                                    color: AppColors.gfBlue,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                // Google login button
                                GFGoogleSignInButton(
                                  onPressed: () {
                                    // TODO: Implement Google sign-in
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text(
                                          'Google sign-in coming soon!',
                                        ),
                                        backgroundColor: Colors.orange,
                                      ),
                                    );
                                  },
                                  height: 55,
                                  width:
                                      MediaQuery.of(context).size.width * 0.7,
                                ),
                                const SizedBox(height: 12),
                                // Apple login button
                                GFAppleSignInButton(
                                  onPressed: () {
                                    // TODO: Implement Apple sign-in
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text(
                                          'Apple sign-in coming soon!',
                                        ),
                                        backgroundColor: Colors.orange,
                                      ),
                                    );
                                  },
                                  height: 55,
                                  width:
                                      MediaQuery.of(context).size.width * 0.7,
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 24),
                          // Continue with email button
                          GFEmailSignInButton(
                            text: 'Continue with email',
                            height: 55,
                            width: MediaQuery.of(context).size.width * 0.8,
                            onPressed: () {
                              setState(() {
                                _showEmailForm = true;
                              });
                            },
                          ),
                        ],
                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ),

              // Loading overlay - only show when loading and no error
              if (authProvider.isLoading && authProvider.errorMessage == null)
                GFLoadingOverlay(message: 'Signing in...', isVisible: true),
            ],
          ),
        );
      },
    );
  }
}
