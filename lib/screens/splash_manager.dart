import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gameflex_mobile/screens/login_screen.dart';
import 'package:gameflex_mobile/screens/home_screen.dart';
import 'package:gameflex_mobile/screens/username_selection_screen.dart';
import 'package:gameflex_mobile/screens/email_verification_screen.dart';
import 'package:gameflex_mobile/providers/auth_provider.dart';
import 'package:gameflex_mobile/theme/app_theme.dart';

class SplashManager extends StatefulWidget {
  const SplashManager({super.key});

  @override
  State<SplashManager> createState() => _SplashManagerState();
}

class _SplashManagerState extends State<SplashManager> {
  bool _authTimeout = false;

  @override
  void initState() {
    super.initState();

    // Start a timeout for auth loading
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        setState(() {
          _authTimeout = true;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Show the main app with auth logic (built-in splash screen will show first)
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // If auth is taking too long, force to login screen
        if (_authTimeout &&
            (authProvider.status == AuthStatus.initial ||
                authProvider.status == AuthStatus.loading)) {
          return const LoginScreen();
        }

        switch (authProvider.status) {
          case AuthStatus.initial:
          case AuthStatus.loading:
            return Scaffold(
              backgroundColor: AppColors.gfGrayBlue900,
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // GameFlex Logo
                    SizedBox(
                      width: 250,
                      height: 250,
                      child: SvgPicture.asset(
                        'assets/images/logos/logo_full_white.svg',
                        fit: BoxFit.contain,
                      ),
                    ),
                    const SizedBox(height: 40),
                    // Loading indicator
                    const CircularProgressIndicator(
                      color: AppColors.gfGreen,
                      strokeWidth: 3,
                    ),
                  ],
                ),
              ),
            );
          case AuthStatus.authenticated:
            return const HomeScreen();
          case AuthStatus.usernameRequired:
            return const UsernameSelectionScreen();
          case AuthStatus.emailVerificationRequired:
            return EmailVerificationScreen(
              email: authProvider.pendingVerificationEmail ?? '',
            );
          case AuthStatus.unauthenticated:
          case AuthStatus.error:
            return const LoginScreen();
        }
      },
    );
  }
}
