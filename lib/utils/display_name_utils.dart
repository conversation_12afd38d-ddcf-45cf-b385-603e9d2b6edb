import '../config/display_name_config.dart';

/// Utilities for handling display names with provider indicators
///
/// This file contains functions for formatting and parsing display names
/// that include provider indicators like [xbox]Username123

/// Format a display name with provider indicator
///
/// Returns the display name with provider brackets if provider is not 'gf'
/// Examples:
/// - formatDisplayName('Username123', 'xbox') -> '[xbox]Username123'
/// - formatDisplayName('Username123', 'gf') -> 'Username123'
/// - formatDisplayName('Username123', null) -> 'Username123'
String formatDisplayName(String displayName, String? provider) {
  if (provider == null ||
      provider.isEmpty ||
      provider == DisplayNameConfig.defaultProvider) {
    return displayName;
  }
  return '[$provider]$displayName';
}

/// Parse a formatted display name to extract provider and username
///
/// Returns a map with 'provider' and 'displayName' keys
/// Examples:
/// - parseDisplayName('[xbox]Username123') -> {'provider': 'xbox', 'displayName': 'Username123'}
/// - parseDisplayName('Username123') -> {'provider': 'gf', 'displayName': 'Username123'}
Map<String, String> parseDisplayName(String formattedDisplayName) {
  final regex = RegExp(r'^\[([^\]]+)\](.+)$');
  final match = regex.firstMatch(formattedDisplayName);

  if (match != null) {
    return {'provider': match.group(1)!, 'displayName': match.group(2)!};
  }

  return {
    'provider': DisplayNameConfig.defaultProvider,
    'displayName': formattedDisplayName,
  };
}

/// Extract just the username part from a formatted display name
///
/// Examples:
/// - extractUsername('[xbox]Username123') -> 'Username123'
/// - extractUsername('Username123') -> 'Username123'
String extractUsername(String formattedDisplayName) {
  final parsed = parseDisplayName(formattedDisplayName);
  return parsed['displayName']!;
}

/// Extract just the provider part from a formatted display name
///
/// Examples:
/// - extractProvider('[xbox]Username123') -> 'xbox'
/// - extractProvider('Username123') -> 'gf'
String extractProvider(String formattedDisplayName) {
  final parsed = parseDisplayName(formattedDisplayName);
  return parsed['provider']!;
}

/// Check if a display name has a provider indicator
///
/// Examples:
/// - hasProviderIndicator('[xbox]Username123') -> true
/// - hasProviderIndicator('Username123') -> false
bool hasProviderIndicator(String formattedDisplayName) {
  return extractProvider(formattedDisplayName) !=
      DisplayNameConfig.defaultProvider;
}

/// Get the display name for a provider type
///
/// Used for UI display purposes
String getProviderDisplayName(String provider) {
  return DisplayNameConfig.getProviderDisplayName(provider);
}

/// Display name option model for the selection UI
class DisplayNameOption {
  final String provider;
  final String displayName;
  final String formattedDisplayName;
  final bool isAvailable;

  DisplayNameOption({
    required this.provider,
    required this.displayName,
    required this.formattedDisplayName,
    required this.isAvailable,
  });

  factory DisplayNameOption.fromJson(Map<String, dynamic> json) {
    return DisplayNameOption(
      provider: json['provider'] as String,
      displayName: json['displayName'] as String,
      formattedDisplayName: json['formattedDisplayName'] as String,
      isAvailable: json['isAvailable'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'provider': provider,
      'displayName': displayName,
      'formattedDisplayName': formattedDisplayName,
      'isAvailable': isAvailable,
    };
  }

  @override
  String toString() {
    return 'DisplayNameOption(provider: $provider, displayName: $displayName, formatted: $formattedDisplayName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DisplayNameOption &&
        other.provider == provider &&
        other.displayName == displayName;
  }

  @override
  int get hashCode => Object.hash(provider, displayName);
}
