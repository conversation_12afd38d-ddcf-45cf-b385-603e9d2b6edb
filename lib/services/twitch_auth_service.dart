import 'dart:developer' as developer;
import 'package:flutter_web_auth_2/flutter_web_auth_2.dart';
import '../services/api_service.dart';
import '../utils/app_logger.dart';

/// Response model for Twitch sign in
class TwitchSignInResponse {
  final bool success;
  final String message;
  final Map<String, dynamic>? user;
  final Map<String, dynamic>? tokens;
  final Map<String, dynamic>? twitchProfile;

  TwitchSignInResponse({
    required this.success,
    required this.message,
    this.user,
    this.tokens,
    this.twitchProfile,
  });

  factory TwitchSignInResponse.fromJson(Map<String, dynamic> json) {
    return TwitchSignInResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      user: json['user'],
      tokens: json['tokens'],
      twitchProfile: json['twitchProfile'],
    );
  }
}

/// Response model for Twitch linking
class TwitchLinkResponse {
  final bool success;
  final String message;
  final String? twitchUsername;
  final String? twitchDisplayName;

  TwitchLinkResponse({
    required this.success,
    required this.message,
    this.twitchUsername,
    this.twitchDisplayName,
  });

  factory TwitchLinkResponse.fromJson(Map<String, dynamic> json) {
    return TwitchLinkResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      twitchUsername: json['twitchUsername'],
      twitchDisplayName: json['twitchDisplayName'],
    );
  }
}

/// Service for handling Twitch authentication
class TwitchAuthService {
  static final TwitchAuthService _instance = TwitchAuthService._internal();
  factory TwitchAuthService() => _instance;
  TwitchAuthService._internal();

  static TwitchAuthService get instance => _instance;

  bool _isLinking = false;

  /// Start Twitch OAuth authentication flow for linking to existing account
  Future<bool> startTwitchOAuth() async {
    if (_isLinking) {
      developer.log('TwitchAuthService: Already linking Twitch account');
      return false;
    }

    _isLinking = true;

    try {
      developer.log('TwitchAuthService: Starting Twitch OAuth flow');
      AppLogger.info('Starting Twitch OAuth authentication');

      // Get the OAuth URL from the backend
      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/twitch/auth',
      );

      if (response.statusCode == 200) {
        final data = ApiService.instance.parseResponse(response);
        final authUrl = data['authUrl'] as String?;

        if (authUrl == null) {
          throw Exception('No auth URL received from server');
        }

        developer.log('TwitchAuthService: Starting OAuth with URL: $authUrl');
        AppLogger.info('Starting Twitch OAuth with flutter_web_auth_2');

        // Use flutter_web_auth_2 to handle the OAuth flow
        final result = await FlutterWebAuth2.authenticate(
          url: authUrl,
          callbackUrlScheme: 'io.gameflex.oauth',
        );

        developer.log(
          'TwitchAuthService: OAuth completed with result: $result',
        );
        AppLogger.info('Twitch OAuth completed');

        // Extract the authorization code from the callback URL
        final uri = Uri.parse(result);
        final code = uri.queryParameters['code'];

        if (code == null) {
          throw Exception('No authorization code received from Twitch');
        }

        developer.log(
          'TwitchAuthService: Got authorization code, linking account',
        );
        AppLogger.info('Linking Twitch account with authorization code');

        // Send the authorization code to the backend to link the account
        final linkResponse = await ApiService.instance.makeAuthenticatedRequest(
          method: 'POST',
          path: '/twitch/link',
          body: {'authCode': code},
        );

        if (linkResponse.statusCode == 200) {
          final linkData = ApiService.instance.parseResponse(linkResponse);
          developer.log(
            'TwitchAuthService: Account linked successfully: $linkData',
          );
          AppLogger.info('Twitch account linked successfully');
          return true;
        } else {
          final errorData = ApiService.instance.parseResponse(linkResponse);
          final errorMessage =
              errorData['error'] ?? 'Failed to link Twitch account';
          developer.log(
            'TwitchAuthService: Failed to link account: $errorMessage',
          );
          AppLogger.error('Failed to link Twitch account: $errorMessage');
          throw Exception(errorMessage);
        }
      } else {
        final errorData = ApiService.instance.parseResponse(response);
        final errorMessage =
            errorData['error'] ?? 'Failed to get Twitch auth URL';
        developer.log(
          'TwitchAuthService: Failed to get auth URL: $errorMessage',
        );
        AppLogger.error('Failed to get Twitch auth URL: $errorMessage');
        throw Exception(errorMessage);
      }
    } catch (e) {
      developer.log('TwitchAuthService: OAuth error: $e');
      AppLogger.error('Twitch OAuth error', error: e);
      return false;
    } finally {
      _isLinking = false;
    }
  }

  /// Sign in with Twitch account
  Future<TwitchSignInResponse> signInWithTwitch() async {
    try {
      developer.log('TwitchAuthService: Starting Twitch Sign In');
      AppLogger.auth('Starting Twitch Sign In');

      // Get the OAuth URL from the backend
      final response = await ApiService.instance.makeRequest(
        method: 'GET',
        path: '/twitch/auth',
      );

      if (response.statusCode != 200) {
        final data = ApiService.instance.parseResponse(response);
        final errorMessage = data['error'] ?? 'Failed to get Twitch auth URL';
        return TwitchSignInResponse(success: false, message: errorMessage);
      }

      final data = ApiService.instance.parseResponse(response);
      final authUrl = data['authUrl'] as String?;

      if (authUrl == null) {
        return TwitchSignInResponse(
          success: false,
          message: 'No auth URL received from server',
        );
      }

      developer.log(
        'TwitchAuthService: Starting Twitch Sign In OAuth with URL: $authUrl',
      );
      AppLogger.info('Starting Twitch Sign In OAuth with flutter_web_auth_2');

      // Use flutter_web_auth_2 to handle the OAuth flow
      final result = await FlutterWebAuth2.authenticate(
        url: authUrl,
        callbackUrlScheme: 'io.gameflex.oauth',
      );

      developer.log(
        'TwitchAuthService: Twitch Sign In OAuth completed with result: $result',
      );
      AppLogger.info('Twitch Sign In OAuth completed');

      // Extract the authorization code from the callback URL
      final uri = Uri.parse(result);
      final code = uri.queryParameters['code'];

      if (code == null) {
        return TwitchSignInResponse(
          success: false,
          message: 'No authorization code received from Twitch',
        );
      }

      developer.log('TwitchAuthService: Got authorization code, signing in');
      AppLogger.info('Signing in with Twitch authorization code');

      // Send the authorization code to the backend for sign in
      final signInResponse = await ApiService.instance.makeRequest(
        method: 'POST',
        path: '/twitch/signin',
        body: {'authCode': code},
      );

      final responseData = ApiService.instance.parseResponse(signInResponse);

      if (signInResponse.statusCode == 200 ||
          signInResponse.statusCode == 201) {
        developer.log('TwitchAuthService: Sign in successful: $responseData');
        AppLogger.auth('Twitch sign in successful');

        return TwitchSignInResponse(
          success: true,
          message: responseData['message'] ?? 'Twitch sign in successful',
          user: responseData['user'],
          tokens: responseData['tokens'],
          twitchProfile: responseData['twitchProfile'],
        );
      } else {
        final errorMessage = responseData['error'] ?? 'Twitch sign in failed';
        developer.log('TwitchAuthService: Sign in failed: $errorMessage');
        AppLogger.error('Twitch sign in failed: $errorMessage');

        return TwitchSignInResponse(success: false, message: errorMessage);
      }
    } catch (e) {
      developer.log('TwitchAuthService: Sign in error: $e');
      AppLogger.error('Twitch sign in error', error: e);

      return TwitchSignInResponse(
        success: false,
        message: 'An error occurred during Twitch sign in: ${e.toString()}',
      );
    }
  }

  /// Check if user is currently linking a Twitch account
  bool get isLinking => _isLinking;
}
