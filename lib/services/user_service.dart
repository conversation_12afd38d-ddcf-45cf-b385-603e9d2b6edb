import '../utils/display_name_utils.dart';
import 'aws_user_service.dart';

/// Service for user-related operations
/// 
/// This service provides a high-level interface for user operations,
/// wrapping the AwsUserService and providing additional functionality
/// like display name management.
class UserService {
  static final UserService _instance = UserService._internal();
  factory UserService() => _instance;
  UserService._internal();

  static UserService get instance => _instance;

  /// Get available display name options for a user
  static Future<List<DisplayNameOption>> getDisplayNameOptions(String userId) async {
    final rawOptions = await AwsUserService.instance.getDisplayNameOptions(userId);
    
    return rawOptions.map((option) => DisplayNameOption.fromJson(option)).toList();
  }

  /// Update user's display name preference
  static Future<bool> updateDisplayName(String userId, String provider, String displayName) async {
    return await AwsUserService.instance.updateDisplayName(userId, provider, displayName);
  }

  /// Get user profile information
  static Future<Map<String, dynamic>?> getUserProfile(String userId) async {
    return await AwsUserService.instance.getUserProfile(userId);
  }

  /// Get user information including profile and stats
  static Future<Map<String, dynamic>?> getUserInfo(String userId) async {
    return await AwsUserService.instance.getUserInfo(userId);
  }

  /// Follow a user
  static Future<bool> followUser(String userId) async {
    return await AwsUserService.instance.followUser(userId);
  }

  /// Unfollow a user
  static Future<bool> unfollowUser(String userId) async {
    return await AwsUserService.instance.unfollowUser(userId);
  }

  /// Check if current user is following another user
  static Future<bool> isFollowing(String userId) async {
    return await AwsUserService.instance.isFollowing(userId);
  }
}
