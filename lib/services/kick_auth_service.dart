import 'dart:developer' as developer;
import 'package:flutter_web_auth_2/flutter_web_auth_2.dart';
import '../services/api_service.dart';
import '../utils/app_logger.dart';

/// Response model for Kick sign in
class KickSignInResponse {
  final bool success;
  final String message;
  final Map<String, dynamic>? user;
  final Map<String, dynamic>? tokens;
  final Map<String, dynamic>? kickProfile;

  KickSignInResponse({
    required this.success,
    required this.message,
    this.user,
    this.tokens,
    this.kickProfile,
  });

  factory KickSignInResponse.fromJson(Map<String, dynamic> json) {
    return KickSignInResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      user: json['user'],
      tokens: json['tokens'],
      kickProfile: json['kickProfile'],
    );
  }
}

/// Response model for Kick linking
class KickLinkResponse {
  final bool success;
  final String message;
  final String? kickUsername;
  final String? kickDisplayName;

  KickLinkResponse({
    required this.success,
    required this.message,
    this.kickUsername,
    this.kickDisplayName,
  });

  factory KickLinkResponse.fromJson(Map<String, dynamic> json) {
    return KickLinkResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      kickUsername: json['kickUsername'],
      kickDisplayName: json['kickDisplayName'],
    );
  }
}

/// Service for handling Kick authentication
class KickAuthService {
  static final KickAuthService _instance = KickAuthService._internal();
  factory KickAuthService() => _instance;
  KickAuthService._internal();

  static KickAuthService get instance => _instance;

  /// Link Kick account to current user
  Future<KickLinkResponse> linkKickAccount(String userId) async {
    try {
      developer.log(
        'KickAuthService: Starting Kick account linking for user: $userId',
      );
      AppLogger.info('Starting Kick account linking');

      // Step 1: Get auth URL from backend
      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/kick/auth',
        queryParams: {'userId': userId},
      );

      if (response.statusCode == 200) {
        final data = ApiService.instance.parseResponse(response);
        final authUrl = data['authUrl'] as String?;

        if (authUrl == null) {
          throw Exception('No auth URL received from server');
        }

        developer.log('KickAuthService: Starting OAuth with URL: $authUrl');
        AppLogger.info('Starting Kick OAuth with flutter_web_auth_2');

        // Use flutter_web_auth_2 to handle the OAuth flow
        final result = await FlutterWebAuth2.authenticate(
          url: authUrl,
          callbackUrlScheme: 'io.gameflex.oauth',
        );

        developer.log('KickAuthService: OAuth completed with result: $result');
        AppLogger.info('Kick OAuth completed');

        // Extract the authorization code and state from the callback URL
        final uri = Uri.parse(result);
        final code = uri.queryParameters['code'];
        final state = uri.queryParameters['state'];

        if (code == null) {
          throw Exception('No authorization code received from Kick');
        }

        if (state == null) {
          throw Exception('No state parameter received from Kick');
        }

        developer.log(
          'KickAuthService: Got authorization code and state, linking account',
        );
        AppLogger.info('Linking Kick account with authorization code');

        // Send the authorization code and state to the backend to link the account
        final linkResponse = await ApiService.instance.makeAuthenticatedRequest(
          method: 'POST',
          path: '/kick/link',
          body: {'authCode': code, 'state': state},
        );

        if (linkResponse.statusCode == 200) {
          final linkData = ApiService.instance.parseResponse(linkResponse);
          developer.log('KickAuthService: Account linked successfully');
          AppLogger.info('Kick account linked successfully');

          return KickLinkResponse.fromJson({
            'success': true,
            'message':
                linkData['message'] ?? 'Kick account linked successfully',
            'kickUsername': linkData['kickUsername'],
            'kickDisplayName': linkData['kickDisplayName'],
          });
        } else {
          final errorData = ApiService.instance.parseResponse(linkResponse);
          throw Exception(errorData['error'] ?? 'Failed to link Kick account');
        }
      } else {
        final errorData = ApiService.instance.parseResponse(response);
        throw Exception(errorData['error'] ?? 'Failed to get Kick auth URL');
      }
    } catch (e) {
      developer.log('KickAuthService: Error linking account: $e');
      AppLogger.error('Failed to link Kick account: $e');

      return KickLinkResponse(
        success: false,
        message: 'Failed to link Kick account: ${e.toString()}',
      );
    }
  }

  /// Sign in with Kick
  Future<KickSignInResponse> signInWithKick() async {
    try {
      developer.log('KickAuthService: Starting Kick Sign In');
      AppLogger.info('Starting Kick Sign In');

      // Step 1: Get auth URL from backend
      final response = await ApiService.instance.makeRequest(
        method: 'GET',
        path: '/kick/auth',
      );

      if (response.statusCode != 200) {
        final errorData = ApiService.instance.parseResponse(response);
        return KickSignInResponse(
          success: false,
          message: errorData['error'] ?? 'Failed to get Kick auth URL',
        );
      }

      final data = ApiService.instance.parseResponse(response);
      final authUrl = data['authUrl'] as String?;

      if (authUrl == null) {
        return KickSignInResponse(
          success: false,
          message: 'No auth URL received from server',
        );
      }

      developer.log(
        'KickAuthService: Starting Kick Sign In OAuth with URL: $authUrl',
      );
      AppLogger.info('Starting Kick Sign In OAuth with flutter_web_auth_2');

      // Use flutter_web_auth_2 to handle the OAuth flow
      final result = await FlutterWebAuth2.authenticate(
        url: authUrl,
        callbackUrlScheme: 'io.gameflex.oauth',
      );

      developer.log(
        'KickAuthService: Kick Sign In OAuth completed with result: $result',
      );
      AppLogger.info('Kick Sign In OAuth completed');

      // Extract the authorization code and state from the callback URL
      final uri = Uri.parse(result);
      final code = uri.queryParameters['code'];
      final state = uri.queryParameters['state'];

      if (code == null) {
        return KickSignInResponse(
          success: false,
          message: 'No authorization code received from Kick',
        );
      }

      if (state == null) {
        return KickSignInResponse(
          success: false,
          message: 'No state parameter received from Kick',
        );
      }

      developer.log(
        'KickAuthService: Got authorization code and state, signing in',
      );
      AppLogger.info('Signing in with Kick authorization code');

      // Send the authorization code and state to the backend for sign in
      final signInResponse = await ApiService.instance.makeRequest(
        method: 'POST',
        path: '/kick/signin',
        body: {'authCode': code, 'state': state},
      );

      if (signInResponse.statusCode == 200) {
        final signInData = ApiService.instance.parseResponse(signInResponse);
        developer.log('KickAuthService: Sign in successful');
        AppLogger.info('Kick sign in successful');

        return KickSignInResponse.fromJson({
          'success': true,
          'message': signInData['message'] ?? 'Kick sign in successful',
          'user': signInData['user'],
          'tokens': signInData['tokens'],
          'kickProfile': signInData['kickProfile'],
        });
      } else {
        final errorData = ApiService.instance.parseResponse(signInResponse);
        return KickSignInResponse(
          success: false,
          message: errorData['error'] ?? 'Failed to sign in with Kick',
        );
      }
    } catch (e) {
      developer.log('KickAuthService: Error during sign in: $e');
      AppLogger.error('Failed to sign in with Kick: $e');

      return KickSignInResponse(
        success: false,
        message: 'Failed to sign in with Kick: ${e.toString()}',
      );
    }
  }
}
