enum LinkedAccountType {
  xbox,
  apple,
  twitch,
  kick;

  String get displayName {
    switch (this) {
      case LinkedAccountType.xbox:
        return 'Xbox Live';
      case LinkedAccountType.apple:
        return 'Apple ID';
      case LinkedAccountType.twitch:
        return 'Twitch';
      case LinkedAccountType.kick:
        return 'Kick';
    }
  }

  static LinkedAccountType fromString(String type) {
    switch (type.toLowerCase()) {
      case 'xbox':
        return LinkedAccountType.xbox;
      case 'apple':
        return LinkedAccountType.apple;
      case 'twitch':
        return LinkedAccountType.twitch;
      case 'kick':
        return LinkedAccountType.kick;
      default:
        throw ArgumentError('Unknown linked account type: $type');
    }
  }
}

class LinkedAccountModel {
  final LinkedAccountType type;
  final String accountId;
  final String displayName;
  final String? profilePictureUrl;
  final String? email; // Optional email for some account types
  final DateTime linkedAt;

  // Convenience getters for component compatibility
  String get provider => type.displayName;

  LinkedAccountModel({
    required this.type,
    required this.accountId,
    required this.displayName,
    this.profilePictureUrl,
    this.email,
    required this.linkedAt,
  });

  /// Create from JSON
  factory LinkedAccountModel.fromJson(Map<String, dynamic> json) {
    return LinkedAccountModel(
      type: LinkedAccountType.fromString(json['type']),
      accountId: json['accountId'],
      displayName: json['displayName'],
      profilePictureUrl: json['profilePictureUrl'],
      linkedAt: DateTime.parse(json['linkedAt']),
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'accountId': accountId,
      'displayName': displayName,
      'profilePictureUrl': profilePictureUrl,
      'linkedAt': linkedAt.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'LinkedAccountModel(type: ${type.displayName}, displayName: $displayName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LinkedAccountModel &&
        other.type == type &&
        other.accountId == accountId;
  }

  @override
  int get hashCode => Object.hash(type, accountId);
}
